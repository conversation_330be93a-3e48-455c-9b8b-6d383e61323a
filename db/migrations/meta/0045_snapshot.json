{"id": "15fa970e-9bd4-4fb2-ac0d-f1966fed9f6e", "prevId": "135c65c4-6d60-4e6b-82ab-4defa75e71ce", "version": "7", "dialect": "postgresql", "tables": {"public.agent_cta_clicks": {"name": "agent_cta_clicks", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('agent_cta_clicks_id_seq'::regclass)"}, "cta_id": {"name": "cta_id", "type": "bigint", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "bigint", "primaryKey": false, "notNull": true}, "prompt_id": {"name": "prompt_id", "type": "bigint", "primaryKey": false, "notNull": true}, "clicked_at": {"name": "clicked_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_ctas": {"name": "agent_ctas", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "standalone_active": {"name": "standalone_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "embedded_active": {"name": "embedded_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "api_active": {"name": "api_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "display_mode": {"name": "display_mode", "type": "display_mode", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'footer'"}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "timing_mode": {"name": "timing_mode", "type": "timing_mode", "typeSchema": "public", "primaryKey": false, "notNull": true}, "response_basis": {"name": "response_basis", "type": "response_basis", "typeSchema": "public", "primaryKey": false, "notNull": true}, "response_number": {"name": "response_number", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "integrations": {"name": "integrations", "type": "jsonb", "primaryKey": false, "notNull": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_ctas_agent_id_idx": {"name": "agent_ctas_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_frontends": {"name": "agent_frontends", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "agent_id": {"name": "agent_id", "type": "bigint", "primaryKey": false, "notNull": false}, "theme": {"name": "theme", "type": "theme_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'dark'"}, "primary_color": {"name": "primary_color", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "image_path": {"name": "image_path", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "background_color": {"name": "background_color", "type": "char(25)", "primaryKey": false, "notNull": false}, "background_path": {"name": "background_path", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "display_font_url": {"name": "display_font_url", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "display_font_name": {"name": "display_font_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "body_font_url": {"name": "body_font_url", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "body_font_name": {"name": "body_font_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "favicon_path": {"name": "favicon_path", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "meta_title": {"name": "meta_title", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "jsonb", "primaryKey": false, "notNull": false}, "meta_keywords": {"name": "meta_keywords", "type": "jsonb", "primaryKey": false, "notNull": false}, "creator_name": {"name": "creator_name", "type": "jsonb", "primaryKey": false, "notNull": false}, "creator_description": {"name": "creator_description", "type": "jsonb", "primaryKey": false, "notNull": false}, "creator_url": {"name": "creator_url", "type": "jsonb", "primaryKey": false, "notNull": false}, "app_icon_path": {"name": "app_icon_path", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "app_icon_color": {"name": "app_icon_color", "type": "char(25)", "primaryKey": false, "notNull": false}, "footer_text": {"name": "footer_text", "type": "jsonb", "primaryKey": false, "notNull": false}, "hide_footer_cta": {"name": "hide_footer_cta", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "footer_cta_label": {"name": "footer_cta_label", "type": "jsonb", "primaryKey": false, "notNull": false}, "footer_cta_url": {"name": "footer_cta_url", "type": "jsonb", "primaryKey": false, "notNull": false}, "embed_hide_header": {"name": "embed_hide_header", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "embed_icon_color": {"name": "embed_icon_color", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": false}, "embed_icon_path": {"name": "embed_icon_path", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "show_media": {"name": "show_media", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "media_collections": {"name": "media_collections", "type": "jsonb", "primaryKey": false, "notNull": false}, "intro_preamble": {"name": "intro_preamble", "type": "jsonb", "primaryKey": false, "notNull": false}, "intro_headline": {"name": "intro_headline", "type": "jsonb", "primaryKey": false, "notNull": false}, "intro_description": {"name": "intro_description", "type": "jsonb", "primaryKey": false, "notNull": false}, "questions_title": {"name": "questions_title", "type": "jsonb", "primaryKey": false, "notNull": false}, "custom_styles": {"name": "custom_styles", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "custom_scripts": {"name": "custom_scripts", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "has_basic_auth": {"name": "has_basic_auth", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "basic_auth_user": {"name": "basic_auth_user", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "basic_auth_password": {"name": "basic_auth_password", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_frontends_agent_id_idx": {"name": "agent_frontends_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_integration_messages": {"name": "agent_integration_messages", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('agent_integration_messages_id_seq'::regclass)"}, "message": {"name": "message", "type": "<PERSON><PERSON><PERSON>(10000)", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "integration_id": {"name": "integration_id", "type": "bigint", "primaryKey": false, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "prompted_at": {"name": "prompted_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_integration_messages_integration_id_idx": {"name": "agent_integration_messages_integration_id_idx", "columns": [{"expression": "integration_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_integration_platforms": {"name": "agent_integration_platforms", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image_path": {"name": "image_path", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "endpoint_url": {"name": "endpoint_url", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "languages": {"name": "languages", "type": "jsonb", "primaryKey": false, "notNull": false}, "has_account": {"name": "has_account", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_secret": {"name": "has_secret", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_cue": {"name": "has_cue", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_welcome": {"name": "has_welcome", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_cta": {"name": "has_cta", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "can_auto_initialize": {"name": "can_auto_initialize", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "param_1_label": {"name": "param_1_label", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "param_2_label": {"name": "param_2_label", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "param_3_label": {"name": "param_3_label", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_integrations": {"name": "agent_integrations", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "bigint", "primaryKey": false, "notNull": false}, "platform_id": {"name": "platform_id", "type": "bigint", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "account": {"name": "account", "type": "<PERSON><PERSON><PERSON>(250)", "primaryKey": false, "notNull": false}, "secret": {"name": "secret", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "token": {"name": "token", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "cue": {"name": "cue", "type": "jsonb", "primaryKey": false, "notNull": false}, "welcome": {"name": "welcome", "type": "jsonb", "primaryKey": false, "notNull": false}, "cta": {"name": "cta", "type": "jsonb", "primaryKey": false, "notNull": false}, "languages": {"name": "languages", "type": "jsonb", "primaryKey": false, "notNull": false}, "auto_initialize": {"name": "auto_initialize", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "param_1": {"name": "param_1", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "param_2": {"name": "param_2", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "param_3": {"name": "param_3", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_integrations_agent_id_idx": {"name": "agent_integrations_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agent_integrations_platform_id_idx": {"name": "agent_integrations_platform_id_idx", "columns": [{"expression": "platform_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_model_providers": {"name": "agent_model_providers", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"agent_model_providers_key_check": {"name": "agent_model_providers_key_check", "value": "(key)::text = ANY ((ARRAY['anthropic'::character varying, 'fireworks'::character varying, 'groq'::character varying, 'openai'::character varying, 'together'::character varying, 'xai'::character varying])::text[])"}}, "isRLSEnabled": false}, "public.agent_models": {"name": "agent_models", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "bigint", "primaryKey": false, "notNull": false}, "provider_model": {"name": "provider_model", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_recommended": {"name": "is_recommended", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "max_tokens": {"name": "max_tokens", "type": "integer", "primaryKey": false, "notNull": true, "default": 4096}, "stop_sequence": {"name": "stop_sequence", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "strip_sequence": {"name": "strip_sequence", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "languages": {"name": "languages", "type": "jsonb", "primaryKey": false, "notNull": false}, "supports_reasoning_effort": {"name": "supports_reasoning_effort", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "supports_verbosity": {"name": "supports_verbosity", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "supports_temperature": {"name": "supports_temperature", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "supports_top_p": {"name": "supports_top_p", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "supports_frequency_penalty": {"name": "supports_frequency_penalty", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "supports_presence_penalty": {"name": "supports_presence_penalty", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "fallback_model_id": {"name": "fallback_model_id", "type": "bigint", "primaryKey": false, "notNull": false}, "input_cost": {"name": "input_cost", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false}, "output_cost": {"name": "output_cost", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false}, "num_credits": {"name": "num_credits", "type": "smallint", "primaryKey": false, "notNull": true}, "seq": {"name": "seq", "type": "smallint", "primaryKey": false, "notNull": true, "default": "'0'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_models_provider_id_idx": {"name": "agent_models_provider_id_idx", "columns": [{"expression": "provider_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"agent_models_type_check": {"name": "agent_models_type_check", "value": "(type)::text = ANY ((ARRAY['limited'::character varying, 'standard'::character varying, 'premium'::character varying, 'reasoning'::character varying, 'admin'::character varying])::text[])"}}, "isRLSEnabled": false}, "public.agent_questions": {"name": "agent_questions", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true}, "question": {"name": "question", "type": "jsonb", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "seq": {"name": "seq", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_questions_agent_id_idx": {"name": "agent_questions_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"agent_questions_agent_id_agents_id_fk": {"name": "agent_questions_agent_id_agents_id_fk", "tableFrom": "agent_questions", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agent_tokens": {"name": "agent_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agent_tokens_agent_id_idx": {"name": "agent_tokens_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "root_domain": {"name": "root_domain", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "vanity_domain": {"name": "vanity_domain", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "has_semantic_search": {"name": "has_semantic_search", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_nonbillable": {"name": "is_nonbillable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "max_memories": {"name": "max_memories", "type": "smallint", "primaryKey": false, "notNull": false}, "model_id": {"name": "model_id", "type": "bigint", "primaryKey": false, "notNull": false}, "model_system_prompt": {"name": "model_system_prompt", "type": "text", "primaryKey": false, "notNull": false}, "model_context_prompt": {"name": "model_context_prompt", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "model_max_tokens": {"name": "model_max_tokens", "type": "integer", "primaryKey": false, "notNull": true}, "model_temperature": {"name": "model_temperature", "type": "double precision", "primaryKey": false, "notNull": false}, "model_top_p": {"name": "model_top_p", "type": "double precision", "primaryKey": false, "notNull": false}, "model_frequency_penalty": {"name": "model_frequency_penalty", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "model_presence_penalty": {"name": "model_presence_penalty", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "model_reasoning_effort": {"name": "model_reasoning_effort", "type": "reasoning_effort_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "model_verbosity": {"name": "model_verbosity", "type": "verbosity_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "persona_name": {"name": "persona_name", "type": "jsonb", "primaryKey": false, "notNull": false}, "persona_tagline": {"name": "persona_tagline", "type": "jsonb", "primaryKey": false, "notNull": false}, "persona_description": {"name": "persona_description", "type": "jsonb", "primaryKey": false, "notNull": false}, "persona_avatar_path": {"name": "persona_avatar_path", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_locked": {"name": "is_locked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_open_source": {"name": "is_open_source", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_selectable": {"name": "is_selectable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_extensible": {"name": "is_extensible", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_template": {"name": "is_template", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_approved": {"name": "is_approved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "marketplace_active": {"name": "marketplace_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "target_worldview": {"name": "target_worldview", "type": "jsonb", "primaryKey": false, "notNull": false}, "default_language": {"name": "default_language", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "default_translation": {"name": "default_translation", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "supported_languages": {"name": "supported_languages", "type": "jsonb", "primaryKey": false, "notNull": false}, "auto_translate": {"name": "auto_translate", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "auto_translate_languages": {"name": "auto_translate_languages", "type": "jsonb", "primaryKey": false, "notNull": false}, "supported_translations": {"name": "supported_translations", "type": "jsonb", "primaryKey": false, "notNull": false}, "use_team_corpus": {"name": "use_team_corpus", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "disable_community_corpus": {"name": "disable_community_corpus", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "categories": {"name": "categories", "type": "jsonb", "primaryKey": false, "notNull": false}, "collections": {"name": "collections", "type": "jsonb", "primaryKey": false, "notNull": false}, "contributors": {"name": "contributors", "type": "jsonb", "primaryKey": false, "notNull": false}, "sources": {"name": "sources", "type": "jsonb", "primaryKey": false, "notNull": false}, "classification_id": {"name": "classification_id", "type": "integer", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "api_response_format": {"name": "api_response_format", "type": "api_response_format", "typeSchema": "public", "primaryKey": false, "notNull": false}, "lock_system_prompt": {"name": "lock_system_prompt", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "strip_markdown": {"name": "strip_markdown", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "bigint", "primaryKey": false, "notNull": false}, "debug": {"name": "debug", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "seq": {"name": "seq", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"agents_model_id_idx": {"name": "agents_model_id_idx", "columns": [{"expression": "model_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "agents_team_id_idx": {"name": "agents_team_id_idx", "columns": [{"expression": "team_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "parent_id": {"name": "parent_id", "type": "bigint", "primaryKey": false, "notNull": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "tagline": {"name": "tagline", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.configs": {"name": "configs", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": false, "notNull": true, "default": "nextval('configs_id_seq'::regclass)"}, "system_prompt": {"name": "system_prompt", "type": "text", "primaryKey": false, "notNull": false}, "max_tokens": {"name": "max_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "temperature": {"name": "temperature", "type": "double precision", "primaryKey": false, "notNull": false}, "top_p": {"name": "top_p", "type": "double precision", "primaryKey": false, "notNull": false}, "model_key": {"name": "model_key", "type": "char(128)", "primaryKey": false, "notNull": false}, "model_id": {"name": "model_id", "type": "bigint", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": false}, "frequency_penalty": {"name": "frequency_penalty", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "presence_penalty": {"name": "presence_penalty", "type": "double precision", "primaryKey": false, "notNull": false, "default": 0}, "reasoning_effort": {"name": "reasoning_effort", "type": "reasoning_effort_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "verbosity": {"name": "verbosity", "type": "verbosity_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}}, "indexes": {"configs_agent_id_idx": {"name": "configs_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.evaluations": {"name": "evaluations", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('evaluations_id_seq'::regclass)"}, "prompt_id": {"name": "prompt_id", "type": "integer", "primaryKey": false, "notNull": false}, "expected_response": {"name": "expected_response", "type": "text", "primaryKey": false, "notNull": false}, "exp_res_at": {"name": "exp_res_at", "type": "timestamp(6)", "primaryKey": false, "notNull": false, "default": "(now() AT TIME ZONE 'utc'::text)"}, "parent_id": {"name": "parent_id", "type": "integer", "primaryKey": false, "notNull": false}, "flagged": {"name": "flagged", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "accuracy_score": {"name": "accuracy_score", "type": "smallint", "primaryKey": false, "notNull": false}, "accuracy_reason": {"name": "accuracy_reason", "type": "text", "primaryKey": false, "notNull": false}, "sympathy_score": {"name": "sympathy_score", "type": "smallint", "primaryKey": false, "notNull": false}, "sympathy_reason": {"name": "sympathy_reason", "type": "text", "primaryKey": false, "notNull": false}, "eval_model": {"name": "eval_model", "type": "text", "primaryKey": false, "notNull": false}, "hide": {"name": "hide", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.item_impressions": {"name": "item_impressions", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('item_impressions_id_seq'::regclass)"}, "frontend": {"name": "frontend", "type": "frontend_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "item_model": {"name": "item_model", "type": "model_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "bigint", "primaryKey": false, "notNull": true}, "prompt_id": {"name": "prompt_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.item_referrals": {"name": "item_referrals", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('item_referrals_id_seq'::regclass)"}, "frontend": {"name": "frontend", "type": "frontend_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "item_model": {"name": "item_model", "type": "model_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "bigint", "primaryKey": false, "notNull": true}, "prompt_id": {"name": "prompt_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": false}, "referred_at": {"name": "referred_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.item_views": {"name": "item_views", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('item_views_id_seq'::regclass)"}, "frontend": {"name": "frontend", "type": "frontend_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "item_model": {"name": "item_model", "type": "model_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "item_id": {"name": "item_id", "type": "bigint", "primaryKey": false, "notNull": true}, "prompt_id": {"name": "prompt_id", "type": "bigint", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "bigint", "primaryKey": false, "notNull": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.prompt_sources": {"name": "prompt_sources", "schema": "", "columns": {"prompt_id": {"name": "prompt_id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('prompt_sources_prompt_id_seq'::regclass)"}, "source_id": {"name": "source_id", "type": "bigint", "primaryKey": false, "notNull": true}, "snippet": {"name": "snippet", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "double precision", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"prompt_sources_prompt_id_foreign": {"name": "prompt_sources_prompt_id_foreign", "tableFrom": "prompt_sources", "tableTo": "prompts", "columnsFrom": ["prompt_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.prompts": {"name": "prompts", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('prompts_id_seq'::regclass)"}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": false}, "prompted_at": {"name": "prompted_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}, "response_started_at": {"name": "response_started_at", "type": "timestamp(6)", "primaryKey": false, "notNull": false}, "response_completed_at": {"name": "response_completed_at", "type": "timestamp(6)", "primaryKey": false, "notNull": false}, "flagged": {"name": "flagged", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "score": {"name": "score", "type": "smallint", "primaryKey": false, "notNull": false}, "config_id": {"name": "config_id", "type": "integer", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "conversation_id": {"name": "conversation_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "translated_prompt": {"name": "translated_prompt", "type": "text", "primaryKey": false, "notNull": false}, "translated_response": {"name": "translated_response", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": false}, "translation": {"name": "translation", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false}, "client": {"name": "client", "type": "client_enum", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'standalone'"}, "prompt_tokens": {"name": "prompt_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "response_tokens": {"name": "response_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "agent_token_id": {"name": "agent_token_id", "type": "integer", "primaryKey": false, "notNull": false}, "chat_tokens": {"name": "chat_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "reasoning": {"name": "reasoning", "type": "text", "primaryKey": false, "notNull": false}, "liked": {"name": "liked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "agent_integration_id": {"name": "agent_integration_id", "type": "bigint", "primaryKey": false, "notNull": false}, "device_id": {"name": "device_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "reasoning_tokens": {"name": "reasoning_tokens", "type": "integer", "primaryKey": false, "notNull": false}, "cached": {"name": "cached", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"prompts_liked_idx": {"name": "prompts_liked_idx", "columns": [{"expression": "liked", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_flagged_idx": {"name": "prompts_flagged_idx", "columns": [{"expression": "flagged", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_agent_id_idx": {"name": "prompts_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_config_id_idx": {"name": "prompts_config_id_idx", "columns": [{"expression": "config_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_session_id_idx": {"name": "prompts_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_token_id_idx": {"name": "prompts_token_id_idx", "columns": [{"expression": "agent_token_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_integration_id_idx": {"name": "prompts_integration_id_idx", "columns": [{"expression": "agent_integration_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prompts_cached_idx": {"name": "prompts_cached_idx", "columns": [{"expression": "cached", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.searches": {"name": "searches", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('searches_id_seq'::regclass)"}, "query": {"name": "query", "type": "text", "primaryKey": false, "notNull": true}, "filters": {"name": "filters", "type": "jsonb", "primaryKey": false, "notNull": false}, "results": {"name": "results", "type": "jsonb", "primaryKey": false, "notNull": false}, "prompt_id": {"name": "prompt_id", "type": "bigint", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "bigint", "primaryKey": false, "notNull": true}, "agent_token_id": {"name": "agent_token_id", "type": "bigint", "primaryKey": false, "notNull": false}, "cached": {"name": "cached", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "searched_at": {"name": "searched_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {"searches_prompt_id_idx": {"name": "searches_prompt_id_idx", "columns": [{"expression": "prompt_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "searches_agent_id_idx": {"name": "searches_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "searches_cached_idx": {"name": "searches_cached_idx", "columns": [{"expression": "cached", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}, "user_agent": {"name": "user_agent", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "host": {"name": "host", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "ip_country": {"name": "ip_country", "type": "char(2)", "primaryKey": false, "notNull": false}, "ip_region": {"name": "ip_region", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "ip_city": {"name": "ip_city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "ip_latitude": {"name": "ip_latitude", "type": "real", "primaryKey": false, "notNull": false}, "ip_longitude": {"name": "ip_longitude", "type": "real", "primaryKey": false, "notNull": false}, "ip_timezone": {"name": "ip_timezone", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "parent_url": {"name": "parent_url", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "parent_host": {"name": "parent_host", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.shares": {"name": "shares", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": false, "notNull": true, "default": "nextval('shares_id_seq'::regclass)"}, "shared_prompt_id": {"name": "shared_prompt_id", "type": "bigint", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "conversation_id": {"name": "conversation_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": false}, "shared_at": {"name": "shared_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_trial": {"name": "is_trial", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "has_custom_corpus": {"name": "has_custom_corpus", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "corpus_api_key": {"name": "corpus_api_key", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "synced_at": {"name": "synced_at", "type": "timestamp(6)", "primaryKey": false, "notNull": true, "default": "(now() AT TIME ZONE 'utc'::text)"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.api_response_format": {"name": "api_response_format", "schema": "public", "values": ["raw", "text", "html", "json"]}, "public.client_enum": {"name": "client_enum", "schema": "public", "values": ["standalone", "embedded", "api", "integration"]}, "public.display_mode": {"name": "display_mode", "schema": "public", "values": ["footer", "modal"]}, "public.response_basis": {"name": "response_basis", "schema": "public", "values": ["conversation_id", "session_id", "device_id", "user_id"]}, "public.timing_mode": {"name": "timing_mode", "schema": "public", "values": ["threshold", "always", "once", "interval"]}, "public.frontend_enum": {"name": "frontend_enum", "schema": "public", "values": ["agent", "social"]}, "public.model_enum": {"name": "model_enum", "schema": "public", "values": ["collection", "contributor", "organization", "source"]}, "public.reasoning_effort_enum": {"name": "reasoning_effort_enum", "schema": "public", "values": ["minimal", "low", "medium", "high"]}, "public.theme_enum": {"name": "theme_enum", "schema": "public", "values": ["dark", "light"]}, "public.verbosity_enum": {"name": "verbosity_enum", "schema": "public", "values": ["low", "medium", "high"]}}, "schemas": {}, "sequences": {"public.agent_cta_clicks_id_seq": {"name": "agent_cta_clicks_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.agent_integration_messages_id_seq": {"name": "agent_integration_messages_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.configs_id_seq": {"name": "configs_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}, "public.evaluations_id_seq": {"name": "evaluations_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.item_impressions_id_seq": {"name": "item_impressions_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.item_referrals_id_seq": {"name": "item_referrals_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.item_views_id_seq": {"name": "item_views_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.prompt_sources_prompt_id_seq": {"name": "prompt_sources_prompt_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.prompts_id_seq": {"name": "prompts_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.searches_id_seq": {"name": "searches_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}, "public.shares_id_seq": {"name": "shares_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "9223372036854775807", "cache": "1", "cycle": false}}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}