{"version": "7", "dialect": "postgresql", "entries": [{"idx": 0, "version": "7", "when": 1741051525147, "tag": "0000_baseline", "breakpoints": true}, {"idx": 1, "version": "7", "when": 1741051608254, "tag": "0001_create_agent_models", "breakpoints": true}, {"idx": 2, "version": "7", "when": 1742663407929, "tag": "0002_add_nonbillable_to_agents", "breakpoints": true}, {"idx": 3, "version": "7", "when": 1742973414547, "tag": "0003_add_hide_header_to_agents", "breakpoints": true}, {"idx": 4, "version": "7", "when": 1743359791046, "tag": "0004_add_meta_fields_to_agents", "breakpoints": true}, {"idx": 5, "version": "7", "when": 1743528453142, "tag": "0005_create_shares_table", "breakpoints": true}, {"idx": 6, "version": "7", "when": 1744141622236, "tag": "0006_create_agent_cta_clicks_table", "breakpoints": true}, {"idx": 7, "version": "7", "when": 1744210102136, "tag": "0007_add_liked_and_feedback_to_prompts", "breakpoints": true}, {"idx": 8, "version": "7", "when": 1744599109887, "tag": "0008_add_auto_translate_languages_to_agents", "breakpoints": true}, {"idx": 9, "version": "7", "when": 1746074556016, "tag": "0009_add_questions_title_to_agents", "breakpoints": true}, {"idx": 10, "version": "7", "when": 1746246898266, "tag": "0010_add_custom_scripts_to_agents", "breakpoints": true}, {"idx": 11, "version": "7", "when": 1749255733585, "tag": "0011_add_reasoning_effort_to_agents", "breakpoints": true}, {"idx": 12, "version": "7", "when": 1749953114872, "tag": "0012_add_fallback_model_id_to_agent_models", "breakpoints": true}, {"idx": 13, "version": "7", "when": 1750643033768, "tag": "0013_add_more_client_types", "breakpoints": true}, {"idx": 14, "version": "7", "when": 1750732209101, "tag": "0014_add_twilio_to_client_types", "breakpoints": true}, {"idx": 15, "version": "7", "when": 1750999279877, "tag": "0015_create_agent_integrations", "breakpoints": true}, {"idx": 16, "version": "7", "when": 1751168647118, "tag": "0016_create_agent_integration_messages_table", "breakpoints": true}, {"idx": 17, "version": "7", "when": 1751170658545, "tag": "0017_add_id_seq_to_agent_integration_messages", "breakpoints": true}, {"idx": 18, "version": "7", "when": 1751518678682, "tag": "0018_add_media_to_agents", "breakpoints": true}, {"idx": 19, "version": "7", "when": 1752028672799, "tag": "0019_add_has_semantic_search_to_agents", "breakpoints": true}, {"idx": 20, "version": "7", "when": 1752621539602, "tag": "0020_add_api_response_format_to_agents", "breakpoints": true}, {"idx": 21, "version": "7", "when": 1752644337266, "tag": "0021_add_basic_auth_credentials_to_agents", "breakpoints": true}, {"idx": 22, "version": "7", "when": 1752799056373, "tag": "0022_add_lock_system_prompt_to_agents", "breakpoints": true}, {"idx": 23, "version": "7", "when": 1752809733984, "tag": "0023_add_debug_to_agents", "breakpoints": true}, {"idx": 24, "version": "7", "when": 1752813660376, "tag": "0024_add_strip_markdown_to_agents", "breakpoints": true}, {"idx": 25, "version": "7", "when": 1752900098781, "tag": "0025_add_integrations_fields", "breakpoints": true}, {"idx": 26, "version": "7", "when": 1752936733238, "tag": "0026_add_languages_and_remove_is_public", "breakpoints": true}, {"idx": 27, "version": "7", "when": 1752987187356, "tag": "0027_add_teams_table", "breakpoints": true}, {"idx": 28, "version": "7", "when": 1752989098809, "tag": "0028_add_primary_key_to_teams_table", "breakpoints": true}, {"idx": 29, "version": "7", "when": 1753412576878, "tag": "0029_remove_ip_address_from_sessions", "breakpoints": true}, {"idx": 30, "version": "7", "when": 1753847298309, "tag": "0030_add_device_id_to_prompts", "breakpoints": true}, {"idx": 31, "version": "7", "when": 1754621832746, "tag": "0031_add_supports_verbosity_to_agent_models", "breakpoints": true}, {"idx": 32, "version": "7", "when": 1754699669894, "tag": "0032_add_font_names_to_agents", "breakpoints": true}, {"idx": 33, "version": "7", "when": 1755322047443, "tag": "0033_add_tags_to_agents", "breakpoints": true}, {"idx": 34, "version": "7", "when": 1755322047443, "tag": "0034_add_reasoning_tokens_to_prompts", "breakpoints": true}, {"idx": 35, "version": "7", "when": 1755333420715, "tag": "0035_make_user_agent_field_longer", "breakpoints": true}, {"idx": 36, "version": "7", "when": 1755566703757, "tag": "0036_add_agent_model_support_booleans", "breakpoints": true}, {"idx": 37, "version": "7", "when": 1756067392063, "tag": "0037_create_agent_ctas", "breakpoints": true}, {"idx": 38, "version": "7", "when": 1756357157226, "tag": "0038_add_foreign_keys_and_indexes", "breakpoints": true}, {"idx": 39, "version": "7", "when": 1756357934301, "tag": "0039_create_agent_frontends", "breakpoints": true}, {"idx": 40, "version": "7", "when": 1756359544373, "tag": "0040_remove_agent_frontend_fields_from_agents", "breakpoints": true}, {"idx": 41, "version": "7", "when": 1756797979975, "tag": "0041_change_primary_color_char_to_varchar", "breakpoints": true}, {"idx": 42, "version": "7", "when": 1756800152145, "tag": "0042_convert_foreign_keys_to_indexes", "breakpoints": true}, {"idx": 43, "version": "7", "when": 1757121417330, "tag": "0043_add_searches_table", "breakpoints": true}, {"idx": 44, "version": "7", "when": 1757195447310, "tag": "0044_add_cached_field_to_searches_and_prompts", "breakpoints": true}, {"idx": 45, "version": "7", "when": 1757212894347, "tag": "0045_add_reasoning_effort_and_verbosity_to_configs", "breakpoints": true}, {"idx": 46, "version": "7", "when": 1757400664736, "tag": "0046_add_cache_ttls_to_agents", "breakpoints": true}, {"idx": 47, "version": "7", "when": 1757529126403, "tag": "0047_Increase size of encrypted fields.", "breakpoints": true}, {"idx": 48, "version": "7", "when": 1757732855029, "tag": "0048_add_license_url_to_agents", "breakpoints": true}, {"idx": 49, "version": "7", "when": 1757811947640, "tag": "0049_add_prioritize_team_corpus_to_agents", "breakpoints": true}]}