name: Reset Staging Database to Production Data

on:
  push:
    branches:
      - staging
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'
        type: choice
        options:
          - info
          - warning
          - debug

jobs:
  Reset-Neon-Branch:
    runs-on: ubuntu-24.04
    steps:
      - uses: neondatabase/reset-branch-action@v1
        id: reset-branch
        with:
          project_id: ${{ vars.NEON_PROJECT_ID }}
          branch: staging # Replace with the branch name or ID you want to reset
          parent: true # always set to true to reset to the latest of the parent branch as only resetting to parent is supported for now
          api_key: ${{ secrets.NEON_API_KEY }}
          cs_role_name: 'default'
      - run: echo Branch ID ${{ steps.reset-branch.outputs.branch_id }}
