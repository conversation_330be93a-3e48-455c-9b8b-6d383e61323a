import QuestionsList from './QuestionsList';

export default function EmptyState({
    t,
    sendMessage,
    introPreamble,
    introHeadline,
    introDescription,
    agentQuestionsTitle,
    agentQuestions,
}) {

    return (

        <div className="flex h-full text-center" id="apg-intro">
            <div
                className="
                    w-full
                    h-full
                    flex
                    flex-col
                    mx-auto
                "
            >

                <div
                    className="
                        m-auto
                        max-w-3xl
                        sm:max-w-4xl
                        md:max-w-5xl
                        lg:max-w-6xl
                        grow
                        flex
                        flex-col
                        items-center
                        justify-center
                    "
                >

                    <h1>
                        <span
                            id="apg-intro-preamble"
                            className="
                                animate-slidein-stagger-1
                                opacity-0
                                block
                                text-xl
                                sm:text-2xl
                                md:text-3xl
                                lg:text-4xl
                                xl:text-5xl
                                text-gray-600
                                dark:text-gray-200
                            "
                        >
                            {introPreamble}
                        </span>
                        <span className="animate-slidein-stagger-2 opacity-0 block">
                            <span
                                id="apg-intro-headline"
                                className="
                                    text-5xl
                                    sm:text-7xl
                                    md:text-8xl
                                    lg:text-9xl
                                    xl:text-10xl
                                    font-display
                                    font-semibold
                                    tracking-tight
                                    text-shadow
                                    text-transparent
                                    gradient-text
                                    animate-gradient
                                "
                            >
                                {introHeadline}
                            </span>
                        </span>
                    </h1>

                    <p
                        id="apg-intro-description"
                        className="
                            animate-slidein-stagger-3
                            opacity-0
                            max-w-2xl
                            mx-auto
                            mt-16
                            text-md
                            sm:text-lg
                            md:text-xl
                            lg:text-2xl
                            leading-8
                            text-gray-500
                            dark:text-gray-400
                        "
                        dangerouslySetInnerHTML={{ __html: introDescription }}
                    >
                    </p>

                </div>

                {agentQuestions && (agentQuestions.length > 0) && (
                    <QuestionsList
                        t={t}
                        sendMessage={sendMessage}
                        agentQuestionsTitle={agentQuestionsTitle}
                        agentQuestions={agentQuestions}
                    />
                )}

            </div>
        </div>
    );
};
