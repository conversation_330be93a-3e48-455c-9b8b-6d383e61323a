'use client';

import * as React from 'react';
import { Track } from 'livekit-client';

import { Toggle } from '../ui/toggle';
import { cn } from '../../../lib/utils';

// TrackToggleProps type definition (converted from TypeScript)
// source: Track.Source
// pending: boolean (optional)

function getSourceIcon(source, enabled, pending = false) {
  if (pending) {
    return 'loading';
  }

  switch (source) {
    case Track.Source.Microphone:
      return enabled ? 'mic on' : 'mic off';
    case Track.Source.Camera:
      return enabled ? 'video on' : 'video off';
    case Track.Source.ScreenShare:
      return 'share';
    default:
      return React.Fragment;
  }
}

export function TrackToggle({ source, pressed, pending, className, ...props }) {
  return (
    <Toggle pressed={pressed} aria-label={`Toggle ${source}`} className={cn(className)} {...props}>
      {getSourceIcon(source, pressed ?? false, pending)}
      {props.children}
    </Toggle>
  );
}
