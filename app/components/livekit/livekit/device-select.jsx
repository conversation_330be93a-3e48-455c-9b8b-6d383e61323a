'use client';

import { LocalAudioTrack, LocalVideoTrack } from 'livekit-client';
import { useMaybeRoomContext, useMediaDeviceSelect } from '@livekit/components-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

// DeviceSelectProps type definition (converted from TypeScript)
// kind: string
// track: LocalVideoTrack | undefined (optional)
// requestPermissions: boolean (optional)
// onError: function (optional)
// initialSelection: string (optional)
// onActiveDeviceChange: function (optional)
// onDeviceListChange: function (optional)
// variant: 'default' | 'small' (optional)

export function DeviceSelect({
  kind,
  track,
  requestPermissions,
  onError,
  // initialSelection,
  // onActiveDeviceChange,
  // onDeviceListChange,
  ...props
}) {
  const size = props.size || 'default';

  const room = useMaybeRoomContext();
  const { devices, activeDeviceId, setActiveMediaDevice } = useMediaDeviceSelect({
    kind,
    room,
    track,
    requestPermissions,
    onError,
  });
  return (
    <Select value={activeDeviceId} onValueChange={setActiveMediaDevice}>
      <SelectTrigger>
        {size !== 'sm' && (
          <SelectValue className="font-mono text-sm" placeholder={`Select a ${kind}`} />
        )}
      </SelectTrigger>
      <SelectContent>
        {devices.map((device) => (
          <SelectItem key={device.deviceId} value={device.deviceId} className="font-mono text-xs">
            {device.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
