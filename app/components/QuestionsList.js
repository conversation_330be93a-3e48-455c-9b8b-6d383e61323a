import { track } from '@vercel/analytics';

export default function EmptyState({
    t,
    sendMessage,
    agentQuestionsTitle,
    agentQuestions,
}) {

    const promptSampleQuestion = (e) => {
        const btn = e.target;
        const questionId = btn.getAttribute('data-question-id');
        const question = btn.innerText;
        track('Prompt:Sample', { questionId: questionId });
        sendMessage({ text: question });
    };

    return (
        <div
            className="
                animate-slidein-stagger-4
                opacity-0
                flex-none
            "
        >

            <h2
                className="
                    mb-4
                    text-gray-500
                    dark:text-gray-400
                "
            >
                {agentQuestionsTitle ?? t('questions.title')}
            </h2>

            <div
                className="
                    relative
                    w-full
                    h-14
                    overflow-x-scroll
                    [mask-image:_linear-gradient(to_right,transparent_0,_black_32px,_black_calc(100%-32px),transparent_100%)]
                "
            >
                <div
                    className="
                        absolute
                        left-0
                        top-0
                        flex
                        whitespace-nowrap
                    "
                >

                    {agentQuestions.map((question, i) => (
                        <span
                            key={i}
                            className="
                                inline-block
                                flex-shrink-0
                                px-2
                            "
                        >
                            <button
                                data-question-id={question.id}
                                className="
                                    px-4
                                    py-2
                                    rounded-md
                                    text-sm
                                    border
                                    text-gray-600
                                    dark:text-gray-400
                                    hover:text-gray-700
                                    dark:hover:text-gray-200
                                    border-gray-300
                                    dark:border-gray-800
                                    dark:hover:border-gray-700
                                    bg-white/90
                                    dark:bg-gray-900/90
                                    hover:bg-gray-100
                                    dark:hover:bg-gray-800
                                    dark:shadow-md
                                "
                                onClick={promptSampleQuestion}
                            >
                                {question.question}
                            </button>
                        </span>
                    ))}


                </div>

            </div>

        </div>
    );
};
