'use client'

import { useState, useEffect, useRef } from 'react';
import { Menu, Transition } from '@headlessui/react';
import { PaperAirplaneIcon, MicrophoneIcon, ArrowPathIcon, EllipsisVerticalIcon } from '@heroicons/react/24/outline';
import TextareaAutosize from 'react-textarea-autosize';
import Link from 'next/link';
import { track } from '@vercel/analytics';
import EmbedAgentClient from "./livekit/embed-iframe/agent-client";

function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
}

const ChatForm = ({
    lng,
    t,
    sendMessage,
    responding,
    setResponding,
    setStreaming,
}) => {

    // const [speechRecognition, setSpeechRecognition] = useState(false);
    const [dictating, setDictating] = useState(false);
    const [input, setInput] = useState('');

    const submitPrompt = (e) => {
        e.preventDefault();
        setResponding(true);
        setStreaming(false);
        sendMessage({ text: input });
        setInput('');
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            submitPrompt(e);
        }
    };

    // useEffect(() => {
    //     let tmpVoiceInput = false;
    //     if (window.SpeechRecognition) {
    //         tmpVoiceInput = new window.SpeechRecognition();
    //     } else if (window.webkitSpeechRecognition) {
    //         tmpVoiceInput = new window.webkitSpeechRecognition();
    //     }
    //     setSpeechRecognition(tmpVoiceInput);
    // }, []);
    //
    // const supportsDictation = (speechRecognition !== false);
    const supportsDictation = true;
    //
    // const dictate = (event) => {
    //
    //     event.preventDefault();
    //     track('Prompt:Dictate');
    //
    //     if (supportsDictation) {
    //
    //         speechRecognition.continuous = false;
    //         speechRecognition.lang = lng;
    //         speechRecognition.interimResults = false;
    //         speechRecognition.maxAlternatives = 1;
    //         speechRecognition.start();
    //         setDictating(true);
    //
    //         speechRecognition.onresult = (e) => {
    //             setInput(e.results[0][0].transcript + '?');
    //             speechRecognition.stop()
    //             setDictating(false);
    //         }
    //
    //     }
    //
    // };

    const reset = (event) => {
        event.preventDefault();
        track('Prompt:Reset');
        window.location = window.location;
    };

    const submitAnimation = responding ? 'animate-pulse' : '';
    // const dictateAnimation = dictating ? 'animate-pulse' : '';

    return (

        <form
            id="apg-prompt"
            className="mt-4 flex grow-0 items-end"
            action="#"
            method="POST"
            onSubmit={submitPrompt}
        >

            <div className="w-full">
                <label htmlFor="prompt" className="sr-only">{t('prompt.label')}</label>
                <TextareaAutosize
                    id="apg-prompt-input"
                    autoComplete="off"
                    autoFocus
                    name="prompt"
                    className="
                        block
                        w-full
                        rounded-3xl
                        text-xl
                        border-0
                        ring-1
                        focus:ring-2
                        ring-inset
                        focus:ring-inset
                        focus:ring-primary-500
                        !px-4
                        !pt-[6px]
                        !pb-[7px]
                        shadow-sm
                        dark:shadow-xl
                        bg-white/50
                        dark:bg-black/25
                        text-gray-900
                        dark:text-white
                        ring-gray-300
                        dark:ring-gray-800
                        placeholder:text-gray-400
                        dark:placeholder:text-gray-600
                    "
                    placeholder={responding ? t('prompt.wait') : t('prompt.placeholder')}
                    required={true}
                    value={input}
                    rows={1}
                    onChange={e => setInput(e.currentTarget.value)}
                    onKeyDown={handleKeyDown}
                    onInput={(e) => {
                        const lineCount = e.target.value.split('\n').length;
                        e.target.rows = lineCount > 10 ? 10 : lineCount;
                    }}
                    disabled={responding}
                />
            </div>

            <button
                id="apg-prompt-submit"
                type="submit"
                disabled={responding}
                className={`
                    ${dictating ? "sm:inline-flex hidden" : "inline-flex"}
                    w-auto
                    items-center
                    justify-center
                    rounded-full
                    bg-primary-500
                    p-2
                    text-md
                    font-semibold
                    hover:bg-primary-600
                    focus-visible:outline
                    focus-visible:outline-2
                    focus-visible:outline-offset-2
                    focus-visible:outline-primary-500
                    ltr:ml-2
                    rtl:mr-2
                    ltr:sm:ml-4
                    rtl:sm:mr-4
                    text-gray-900
                    dark:text-white
                    shadow-md
                    dark:shadow-xl
                    ${submitAnimation}
                `}
            >
                <span className="sr-only">{t('prompt.submit')}</span>
                <PaperAirplaneIcon className="h-6 w-6 text-white relative left-0.5" aria-hidden="true" />
            </button>

            {/*{supportsDictation && (*/}
            {/*    <button*/}
            {/*        id="apg-prompt-dictate"*/}
            {/*        disabled={dictating}*/}
            {/*        className={`                        */}
            {/*            ${dictating ? "sm:inline-flex" : "hidden"} */}
            {/*            sm:inline-flex*/}
            {/*             w-auto*/}
            {/*            items-center*/}
            {/*            justify-center*/}
            {/*            rounded-full*/}
            {/*            p-2*/}
            {/*            text-md*/}
            {/*            font-semibold*/}
            {/*            focus-visible:outline*/}
            {/*            focus-visible:outline-2*/}
            {/*            focus-visible:outline-offset-2*/}
            {/*            focus-visible:outline-primary-500*/}
            {/*            ltr:ml-2*/}
            {/*            rtl:mr-2*/}
            {/*            ltr:sm:ml-4*/}
            {/*            rtl:sm:mr-4*/}
            {/*            ${dictating ? "bg-primary-500 sm:bg-gray-500" : "bg-gray-500 dark:bg-gray-800"}*/}
            {/*            text-gray-900*/}
            {/*            dark:text-white*/}
            {/*            shadow-md*/}
            {/*            dark:shadow-xl*/}
            {/*            hover:bg-gray-400*/}
            {/*            dark:hover:bg-gray-700*/}
            {/*            ${dictateAnimation}*/}
            {/*        `}*/}
            {/*        onClick={dictate}*/}
            {/*    >*/}
            {/*        <span className="sr-only">{t('prompt.dictate')}</span>*/}
            {/*        <MicrophoneIcon className="h-6 w-6 text-white" aria-hidden="true" />*/}
            {/*    </button>*/}
            {/*)}*/}

            <EmbedAgentClient appConfig={{
                companyName: 'LiveKit',
                pageTitle: 'LiveKit Embed',
                pageDescription: 'A web embed connected to an agent, built with LiveKit',

                supportsChatInput: true,
                supportsVideoInput: true,
                supportsScreenShare: true,
                isPreConnectBufferEnabled: true,

                logo: '/lk-logo.svg',
                accent: '#002cf2',
                logoDark: '/lk-logo-dark.svg',
                accentDark: '#1fd5f9',
                startButtonText: 'Start call',
            }} />

            <button
                id="apg-prompt-reset"
                type="reset"
                className="
                    hidden
                    sm:inline-flex
                    w-auto
                    items-center
                    justify-center
                    rounded-full
                    p-2
                    text-md
                    font-semibold
                    focus-visible:outline
                    focus-visible:outline-2
                    focus-visible:outline-offset-2
                    focus-visible:outline-primary-500
                    ltr:ml-2
                    rtl:mr-2
                    ltr:sm:ml-4
                    rtl:sm:mr-4
                    bg-gray-500
                    dark:bg-gray-800
                    text-gray-900
                    dark:text-white
                    shadow-md
                    dark:shadow-xl
                    hover:bg-gray-400
                    dark:hover:bg-gray-700
                "
                onClick={reset}
            >
                <span className="sr-only">{t('prompt.reset')}</span>
                <ArrowPathIcon className="h-6 w-6 text-white" aria-hidden="true" />
            </button>

            <Menu
                id="apg-three-dot-menu"
                as="div"
                className="relative sm:hidden"
            >
                <div>
                    <Menu.Button
                        disabled={responding}
                        className="
                          inline-flex
                          w-auto
                            items-center
                            justify-center
                            rounded-full
                            p-2
                            text-md
                            font-semibold
                            focus-visible:outline
                            focus-visible:outline-2
                            focus-visible:outline-offset-2
                            focus-visible:outline-primary-500
                            ltr:ml-2
                            rtl:mr-2
                            ltr:sm:ml-4
                            rtl:sm:mr-4
                            bg-gray-500
                            dark:bg-gray-800
                            text-gray-900
                            dark:text-white
                            shadow-md
                            dark:shadow-xl
                            hover:bg-gray-400
                            dark:hover:bg-gray-700
                        "
                        aria-label={t('prompt.openMenu')}
                    >
                        <EllipsisVerticalIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </Menu.Button>
                </div>

                <Transition
                    as="div"
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                >
                    <Menu.Items
                        className="
                           absolute
                           ltr:right-0
                           rtl:left-0
                           z-10
                           bottom-10
                           mb-2
                           w-52
                           g-white
                           dark:bg-gray-900
                           bg-white/95
                           shadow-lg
                           rounded-md
                           ring-1
                           ring-white
                           dark:ring-black
                           ring-opacity-5
                           focus:outline-none
                           border
                           border-gray-100
                           dark:border-gray-800
                         "
                    >
                            <Menu.Item>
                                {({ active }) => (
                                    <Link
                                        href="#"
                                        className={classNames(
                                            active ? 'bg-primary-500 text-white' : 'text-gray-800 dark:text-gray-300',
                                            'gap-x-2 flex items-center px-4 py-2 text-md'
                                        )}
                                        onClick={reset}
                                    >
                                        <ArrowPathIcon className="h-5 w-5 text-gray-900 dark:text-white" aria-hidden="true" />
                                        New Conversation
                                    </Link>
                                )}
                            </Menu.Item>
                            {supportsDictation && (
                                <Menu.Item>
                                    {({ active, close }) => (
                                        <Link
                                            disabled={dictating}
                                            href="#"
                                            className={classNames(
                                                active ? 'bg-primary-500 text-white' : 'text-gray-800 dark:text-gray-300',
                                                'gap-x-2 flex items-center px-4 py-2 text-md'
                                            )}
                                            onClick={(e) => {
                                                e.preventDefault();
                                                close();
                                                dictate(e);
                                            }}
                                        >
                                            <MicrophoneIcon className="h-5 w-5 text-gray-900 dark:text-white" aria-hidden="true" />
                                            Dictate
                                        </Link>
                                    )}
                                </Menu.Item>
                            )}
                    </Menu.Items>
                </Transition>
            </Menu>

        </form>

    );
};

export default ChatForm;
