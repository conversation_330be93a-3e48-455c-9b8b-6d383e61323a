/* The below helpers are almost entirely AI generated */

import { Redis } from '@upstash/redis';
import {
    debug,
    envBoolean,
} from './helpers';

// Initialize Redis client lazily to ensure environment variables are loaded
let redis = new Redis({
    url: process.env.CACHE_KV_REST_API_URL,
    token: process.env.CACHE_KV_REST_API_TOKEN,
});

/**
 * Generates a deterministic cache key from a JavaScript object
 * @param {any} obj - The object to generate a cache key from
 * @param agentId
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {Promise<string>} A deterministic cache key
 */
export async function generateCacheKey(obj, agentId, prefix = null) {

    prefix = getCachePrefix(agentId, prefix);

    // Handle null/undefined
    if (obj === null || obj === undefined) {
        return prefix ? `${prefix}:null` : 'null';
    }

    // Convert object to a deterministic string representation
    const normalizedString = normalizeObject(obj);

    // Create a hash using Web Crypto API (compatible with Edge functions)
    const hash = await createHash(normalizedString);

    return prefix ? `${prefix}:${hash}` : hash;
}

/**
 * Creates a SHA-256 hash using Web Crypto API (Edge runtime compatible)
 * @param {string} data - The string to hash
 * @returns {Promise<string>} Hex-encoded hash
 */
async function createHash(data) {
    // Use Web Crypto API which is available in Edge functions
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);

    // Convert ArrayBuffer to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;
}

/**
 * Recursively normalizes an object to create a deterministic string representation
 * @param {any} obj - The object to normalize
 * @returns {string} Normalized string representation
 */
function normalizeObject(obj) {
    if (obj === null) return 'null';
    if (obj === undefined) return 'undefined';

    const type = typeof obj;

    switch (type) {
        case 'boolean':
        case 'number':
        case 'string':
            return `${type}:${obj}`;

        case 'function':
            return `function:${obj.toString()}`;

        case 'object':
            if (obj instanceof Date) {
                return `date:${obj.toISOString()}`;
            }

            if (Array.isArray(obj)) {
                return `array:[${obj.map(normalizeObject).join(',')}]`;
            }

            // For regular objects, sort keys for deterministic output
            const sortedKeys = Object.keys(obj).sort();
            const pairs = sortedKeys.map(key => `${key}:${normalizeObject(obj[key])}`);
            return `object:{${pairs.join(',')}}`;

        default:
            return `${type}:${String(obj)}`;
    }
}

/**
 * Sets a cached value with TTL
 * @param {any} keyObject - Object to generate cache key from
 * @param {any} data - Data to cache
 * @param {number|null} ttlSeconds - Time to live in seconds (null means no caching)
 * @param agentId
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {Promise<string|null>} The cache key that was used, or null if not cached
 */
export async function setCachedValue(keyObject, data, ttlSeconds, agentId, prefix = null) {
    // If ttlSeconds is null or undefined, don't cache
    if (ttlSeconds == null) {
        return null;
    }

    try {
        const cacheKey = await generateCacheKey(keyObject, agentId, prefix);
        debug(envBoolean(process.env.SHOULD_LOG), 'SET CACHE KEY', cacheKey);
        debug(envBoolean(process.env.SHOULD_LOG), 'CACHE KEY OBJECT', keyObject);

        // Create cache entry with timestamp and data
        const cacheEntry = {
            data,
            timestamp: Date.now(),
            ttl: ttlSeconds * 1000 // Convert to milliseconds
        };

        // Serialize the cache entry and validate it can be parsed
        const serializedEntry = JSON.stringify(cacheEntry);

        // Test that we can parse it back (this will throw if there are issues)
        JSON.parse(serializedEntry);

        // Store in Redis with TTL - use set with EX option for better compatibility
        await redis.set(cacheKey, serializedEntry, { ex: ttlSeconds });

        return cacheKey;
    } catch (error) {
        console.error('Error setting cached value:', error);
        console.error('Data that failed to cache:', data);
        return null;
    }
}

/**
 * Gets a cached value with TTL validation
 * @param {any} keyObject - Object to generate cache key from
 * @param {number} maxAgeSeconds - Maximum age in seconds (if data is older, return null)
 * @param agentId
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {Promise<any|null>} The cached data or null if expired/not found
 */
export async function getCachedValue(keyObject, maxAgeSeconds, agentId, prefix = null) {

    const cacheKey = await generateCacheKey(keyObject, agentId, prefix);
    debug(envBoolean(process.env.SHOULD_LOG),'GET CACHE KEY', cacheKey);
    debug(envBoolean(process.env.SHOULD_LOG), 'CACHE KEY OBJECT', keyObject);

    try {
        const cachedValue = await redis.get(cacheKey);

        if (!cachedValue) {
            return null;
        }

        let cacheEntry;

        // Handle both string and object responses from Upstash Redis
        if (typeof cachedValue === 'string') {
            // Check if the cached string looks like "[object Object]"
            if (cachedValue === '[object Object]' || cachedValue.startsWith('[object ')) {
                console.error('Cached value appears to be a stringified object:', cachedValue);
                // Delete the corrupted entry
                await redis.del(cacheKey);
                return null;
            }

            // Parse the JSON string
            cacheEntry = JSON.parse(cachedValue);
        } else if (typeof cachedValue === 'object') {
            // Upstash Redis automatically deserialized the JSON for us
            cacheEntry = cachedValue;
        } else {
            console.error('Cached value has unexpected type:', typeof cachedValue, cachedValue);
            // Try to delete the corrupted entry
            await redis.del(cacheKey);
            return null;
        }

        // Validate cache entry structure
        if (!cacheEntry || typeof cacheEntry !== 'object' || !cacheEntry.hasOwnProperty('timestamp') || !cacheEntry.hasOwnProperty('data')) {
            console.error('Invalid cache entry structure:', cacheEntry);
            await redis.del(cacheKey);
            return null;
        }

        const now = Date.now();
        const age = (now - cacheEntry.timestamp) / 1000; // Convert to seconds

        // Check if data is older than maxAgeSeconds
        if (age > maxAgeSeconds) {
            // Optionally delete expired entry
            await redis.del(cacheKey);
            return null;
        }

        return cacheEntry.data;
    } catch (error) {
        console.error('Error retrieving cached value:', error);
        console.error('Cache key:', cacheKey);

        // If it's a JSON parse error, log the raw cached string
        if (error instanceof SyntaxError) {
            try {
                const rawValue = await redis.get(cacheKey);
                console.error('Raw cached value that failed to parse:', rawValue);
                console.error('Type of raw cached value:', typeof rawValue);
                // Delete the corrupted entry
                await redis.del(cacheKey);
            } catch (deleteError) {
                console.error('Error deleting corrupted cache entry:', deleteError);
            }
        }

        return null;
    }
}

/**
 * Deletes a cached value
 * @param {any} keyObject - Object to generate cache key from
 * @param agentId
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deleteCachedValue(keyObject, agentId, prefix = null) {
    const cacheKey = await generateCacheKey(keyObject, agentId, prefix);
    const result = await redis.del(cacheKey);
    return result > 0;
}

/**
 * Checks if a cached value exists and is not expired
 * @param {any} keyObject - Object to generate cache key from
 * @param {number} maxAgeSeconds - Maximum age in seconds
 * @param agentId
 * @param {string} prefix - Optional prefix for the cache key
 * @returns {Promise<boolean>} True if exists and not expired
 */
export async function hasCachedValue(keyObject, maxAgeSeconds, agentId, prefix = null) {
    const value = await getCachedValue(keyObject, maxAgeSeconds, agentId, prefix);
    return value !== null;
}

export function getCacheTtl(headers, query, agentTtl = null) {
    const ttl = query.cache_ttl ?? headers['x-cache-ttl'] ?? agentTtl ?? null;
    return ttl ? parseInt(ttl) : null;
}

/**
 * Clear all cache entries (useful for debugging corrupted cache)
 * @returns {Promise<number>} Number of keys deleted
 */
export async function clearAllCache() {
    try {
        const keys = await redis.keys('*');
        if (keys.length === 0) {
            return 0;
        }
        const result = await redis.del(...keys);
        // console.log(`Cleared ${result} cache entries`);
        return result;
    } catch (error) {
        console.error('Error clearing cache:', error);
        return 0;
    }
}

function getCachePrefix(agentId, prefix) {
    return process.env.NEXT_PUBLIC_ENV + ':' + agentId + ':' + (prefix ?? '');
}
