import {
    htmlToMarkdown,
    transformCompletion
} from './format';
import { JSD<PERSON> } from 'jsdom';
import { get408Response } from './response';
import {
    debug,
    envBoolean,
} from './helpers';
import { translateToLanguage } from './language';
import { generateText } from 'ai';
import {
    setCompletionCache,
    setCompletionFinish,
    getFallbackCompletionQuery,
    decorateCompletionParams,
    parseCompletion,
} from './completion-utils';
import { waitUntil } from '@vercel/functions';

export async function getNonStreamingResponse(
    completionQry,
    payload,
    promptId,
    prompt,
    messages,
    realTimeTranslation,
    responseParams,
    responseOptions,
    fallbackModelId,
    cachedCompletion,
    cacheTtl,
    agent,
    configId,
    startTime,
) {

    let completion = null;
    let translatedResponse = null;
    let response = null;
    let reasoning = null;

    if (cachedCompletion) {

        response = realTimeTranslation ? htmlToMarkdown(cachedCompletion, new JSDOM().window.document) : cachedCompletion;

    } else {

        completion = await getNonStreamingCompletion(completionQry, fallbackModelId);
        if (!completion) {
            return get408Response();
        }

        const tmpParts = parseCompletion(completion.text);
        response = tmpParts.response;
        reasoning = tmpParts.reasoning;
        debug(envBoolean(process.env.SHOULD_LOG), 'COMPLETION', completion);

        if (realTimeTranslation) {
            translatedResponse = response;
            response = await translateToLanguage(
                translatedResponse,
                process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
                payload.metadata.language
            );
        }

        if (cacheTtl) {
            await setCompletionCache(
                cacheTtl,
                response,
                messages,
                agent.id,
                configId,
                payload.metadata.language,
                payload.metadata.translation
            );
        }

    }

    waitUntil(setCompletionFinish(
        promptId,
        prompt,
        messages,
        response,
        reasoning,
        realTimeTranslation,
        payload.metadata.language,
        completion?.totalUsage ?? null,
        !!cachedCompletion,
        translatedResponse
    ));

    const transformedCompletion = transformCompletion(response, (payload.response_format.type === 'html'), agent.strip_markdown);

    let formattedCompletion = transformedCompletion;
    if (payload.response_format.type === 'json') {
        formattedCompletion = responseParams;
        formattedCompletion.choices = [
            {
                index: 0,
                message: {
                    role: 'assistant',
                    content: transformedCompletion,
                },
                logprobs: null,
                finish_reason: 'stop'
            }
        ];
        formattedCompletion.cached = process.env.CACHED_RESPONSE;
        formattedCompletion = JSON.stringify(decorateCompletionParams(formattedCompletion, messages, response, reasoning, startTime));
    }
    debug(envBoolean(process.env.SHOULD_LOG), 'NON-STREAMING RESPONSE', JSON.stringify(formattedCompletion, 4));

    return new Response(
        formattedCompletion,
        responseOptions
    );

}

async function getNonStreamingCompletion(
    completionQry,
    fallbackModelId
) {
    try {
        return await generateText(completionQry);
    } catch (e) {
        debug(envBoolean(process.env.SHOULD_LOG), 'ERROR', e);
        if (fallbackModelId) {
            return await generateText(await getFallbackCompletionQuery(completionQry, fallbackModelId));
        } else {
            return false;
        }
    }
}
