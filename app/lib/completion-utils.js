import {
    getCachedValue,
    setCachedValue
} from './cache';
import {
    debug,
    getMessageText,
    envBoolean,
} from './helpers';
import {
    getAgentModelById,
    updatePrompt
} from './db';
import { isNull } from 'drizzle-orm';
import * as tables from '../../db/schema';
import { getModelProvider } from "./agent";

export const REASONING_START_SEQUENCE = '<think>';
export const REASONING_END_SEQUENCE = '</think>';

export async function getCachedCompletion(
    cacheTtl,
    messages,
    agentId,
    configId,
    language,
    translation
) {

    let cachedCompletion = null;

    if (cacheTtl) {
        cachedCompletion = await getCachedValue(
            normalizeCacheKeyObject(messages, configId, language, translation),
            cacheTtl,
            agentId,
            'completion'
        );
        debug(envBoolean(process.env.SHOULD_LOG), 'CACHED RESPONSE', cachedCompletion);
    }

    return cachedCompletion;

}

export async function setCompletionCache(
    cacheTtl,
    response,
    messages,
    agentId,
    configId,
    language,
    translation
) {
    if (cacheTtl) {
        const valueToCache = response?.steps?.[0]?.content?.[0]?.text ?? response?.text ?? response;
        if (valueToCache) {
            debug(envBoolean(process.env.SHOULD_LOG), 'SET CACHED RESPONSE', valueToCache);
            await setCachedValue(
                normalizeCacheKeyObject(messages, configId, language, translation),
                valueToCache,
                cacheTtl,
                agentId,
                'completion'
            );
        }
    }
}

function normalizeCacheKeyObject(messages, configId, language, translation) {
    return {
        messages,
        configId,
        language,
        translation,
    };
}

// Log the time the prompt response finishes
export async function setCompletionFinish(
    promptId,
    prompt,
    messages,
    response,
    reasoning,
    realTimeTranslation,
    lng,
    usage,
    cachedResponse = false,
    translatedResponse = null
) {

    debug(envBoolean(process.env.SHOULD_LOG), 'USAGE', usage);
    debug(envBoolean(process.env.SHOULD_LOG), 'REASONING', reasoning);
    debug(envBoolean(process.env.SHOULD_LOG), 'RESPONSE', response);
    debug(envBoolean(process.env.SHOULD_LOG), 'TRANSLATED RESPONSE', translatedResponse);

    reasoning = (reasoning && (reasoning.length > 0)) ?
        reasoning.replace(REASONING_START_SEQUENCE, '').replace(REASONING_END_SEQUENCE, '').trim() :
        null
    ;

    // response = response.trim();
    // if (realTimeTranslation && !translatedResponse) {
    //     translatedResponse = await translateToLanguage(response, process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE, lng);
    // }

    if (response && (response.length > 0)) {

        const promptTokens = cachedResponse ? null : estimateTokens(prompt);
        const chatTokens = cachedResponse ? null : usage.inputTokens ?? estimateTokens(messages);
        const reasoningTokens = cachedResponse ? null : usage.reasoningTokens ?? estimateTokens(reasoning);
        const responseTokens = cachedResponse ? null : usage.outputTokens ?? estimateTokens(reasoning + response);

        debug(envBoolean(process.env.SHOULD_LOG), `Prompt #${promptId} completed`);
        return updatePrompt(
            promptId,
            {
                response: response,
                translated_response: translatedResponse,
                reasoning: reasoning,
                prompt_tokens: promptTokens,
                chat_tokens: chatTokens,
                response_tokens: responseTokens,
                reasoning_tokens: reasoningTokens,
                cached: !!cachedResponse,
            },
            'response_completed_at',
            [
                isNull(tables.prompts.response),
            ]
        );

    }

}

export function estimateTokens(strOrArray) {
    if (!strOrArray) {
        return null;
    }
    if (Array.isArray(strOrArray)) {
        let tokens = 0;
        strOrArray.forEach(message => {
            tokens += estimateTokens(getMessageText(message));
        });
        return Math.ceil(tokens);
    }
    return Math.ceil(strOrArray.length / 4);
}

export async function getFallbackCompletionQuery(completionQry, fallbackModelId) {
    debug(envBoolean(process.env.SHOULD_LOG), 'COMPLETION FAILED, USING FALLBACK MODEL' + fallbackModelId);
    const model = await getAgentModelById(fallbackModelId);
    const provider = getModelProvider(model);
    completionQry.model = getCompletionQueryModel(model, provider);
    return completionQry;
}

export function getCompletionQueryModel(model, provider) {
    return provider.chat ? provider.chat(model.provider_model) : provider.chatModel(model.provider_model);
}


export function decorateCompletionParams(completionParams, messages, rawCompletion, reasoning, startTime) {

    const chatTokens = process.env.CACHED_RESPONSE ? null : estimateTokens(messages);
    const responseTokens = process.env.CACHED_RESPONSE ? null : (rawCompletion ? estimateTokens(rawCompletion) : 0);
    // TODO: we're using the parsed reasoning text from models that support it, but many models just return the raw tokens ... and we're not capturing that now!
    const reasoningTokens = process.env.CACHED_RESPONSE ? null : (reasoning ? estimateTokens(reasoning) : 0);
    const endTime = performance.now();
    const startTs = new Date(performance.timeOrigin + startTime);
    const endTs = new Date(performance.timeOrigin + endTime);

    completionParams.usage = {
        prompt_tokens: chatTokens,
        completion_tokens: responseTokens,
        total_tokens: process.env.CACHED_RESPONSE ? null : (chatTokens + responseTokens),
        completion_tokens_details: {
            reasoning_tokens: reasoningTokens,
            accepted_prediction_tokens: process.env.CACHED_RESPONSE ? null : 0,
            rejected_prediction_tokens: process.env.CACHED_RESPONSE ? null : 0
        },
    };

    completionParams.timings = {
        start: startTs.toISOString(),
        end: endTs.toISOString(),
        elapsed: endTime - startTime,
    };

    return completionParams;

}

export function parseCompletion(completion) {
    let response = completion;
    let reasoning = null;
    const reasoningEndPos = completion.indexOf(REASONING_END_SEQUENCE);
    if (reasoningEndPos !== -1) {
        response = completion.substring(reasoningEndPos + REASONING_END_SEQUENCE.length).trim();
        reasoning = completion.substring(REASONING_START_SEQUENCE.length, reasoningEndPos).trim();
    }
    return { response, reasoning };
}
