import {
    getAgentByHost,
    getAgentTokenFromHeader,
    getAgentTokenFromQuery,
    getModelProvider,
} from './agent';
import {
    detectLanguage,
    translateToLanguage
} from './language';
import {
    agentIsInactive,
    camelToSnake,
    debug,
    shouldLog,
    envBoolean,
    getMessageCta,
    getMessageText,
    replaceTokens,
    shouldIncludeSource,
    shouldUseRealTimeTranslation,
} from './helpers';
import {
    getPayloadValidation,
    requestIsSameOrigin
} from './request';
import {
    getAgentConfig,
    getAgentModelById,
    getAgentModelByKey,
    getCtas,
    getExtensibleAgentById,
    getRecentPrompts,
    getResponseCounts,
    getSessionById,
    insertAgentConfig,
    insertPrompt,
    insertPromptSource,
    insertSession,
    updatePrompt,
} from './db';
import {
    get403Response,
    get422Response,
    get503Response,
} from './response';
import {
    filterUniqueDocs,
    formatDocumentIds,
    prependMetaFilterCombinator,
    searchCorpus,
} from './vectara';
import { expandRagDocumentAttrs } from './rag';
import { decode } from 'html-entities';
import { getSessionValue } from './session';
import { waitUntil } from '@vercel/functions';
import { convertToModelMessages } from 'ai';
import { headers } from 'next/headers';
import { decryptBackend } from './crypt';
import { getCacheTtl } from './cache';
import { getStreamingResponse } from './completion-streaming';
import { getNonStreamingResponse } from './completion-nonstreaming';
import {
    getCompletionQueryModel,
    getCachedCompletion,
} from './completion-utils';
import { transformCompletion } from './format';

const API_KEY_HEADER = 'x-api-key';
const API_KEY_PARAM = 'api_key';
const CONTENT_TYPES = {
    text: 'text/plain',
    html: 'text/html',
    json: 'application/json',
    raw: 'text/plain',
};
const EMPTY_RAG_CONTEXT = {sources:[],text:''};
const SYSTEM_ROLES = ['system', 'developer'];
const STRIP_MARKDOWN_INSTRUCTIONS = "\n\nDeliver the response in plain text without any Markdown or formatting. Provide the output as raw text.";
const MODEL_PARAM_DEFAULTS = {
    maxTokens: process.env.COMPLETION_API_DEFAULT_MAX_TOKENS,
    temperature: 1,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
};
process.env.SHOULD_LOG = true;

// Payload schema validator -- had to precompile b/c the compiler won't run on edge runtime
const validatePayload = require('../api/v1/chat/completions/validation');

// Handle the chat completion
export async function handleCompletion(payload, req, integration = null) {

    const startTime = performance.now();

    debug(true, 'REQUEST', payload);

    delete payload.id;
    delete payload.trigger;
    const validation = await getPayloadValidation(payload, validatePayload);
    if (validation) {
        debug(true, 'VALIDATION ERROR', validation);
        return get422Response(validation);
    }

    // Get Agent by hostname; bail if there's no match
    const AGENT = await getAgentByHost(req.headers.get('host'));
    // debug(envBoolean(process.env.SHOULD_LOG), 'AGENT', AGENT);
    if (
        agentIsInactive(AGENT) ||
        (
            integration &&
            (integration.agent_id !== AGENT.id)
        )
    ) {
        return get503Response();
    }

    // Bail if this is an API request and the API token is invalid
    const sameOrigin = requestIsSameOrigin(req.headers);
    const tokenId = await getAgentTokenFromHeader(AGENT.id, req.headers, API_KEY_HEADER) ||
        await getAgentTokenFromQuery(AGENT.id, req.nextUrl.searchParams, API_KEY_PARAM)
    ;
    if (!sameOrigin && !tokenId) {
        return get403Response();
    }

    const headers = Object.fromEntries(await req.headers.entries());
    const query = Object.fromEntries(req.nextUrl.searchParams.entries());
    process.env.SHOULD_LOG = shouldLog(payload, query, headers, AGENT);
    const cacheTtl = getCacheTtl(headers, query, AGENT.completion_cache_ttl);
    debug(envBoolean(process.env.SHOULD_LOG), 'REQUEST', { body: payload, headers: headers, query: query });
    debug(envBoolean(process.env.SHOULD_LOG), 'CACHE TTL', cacheTtl);

    // If the response-format header is present, use that to set payload value
    if (headers['x-response-format']) {
        payload.response_format = { type: headers['x-response-format'] };
    }

    // Set defaults
    const metadataDefaults = {
        anonymous: false,
        conversation: null,
        max_memories: null,
        parent_url: null,
        parent_host: null,
        session: null,
        device: null,
        translation: AGENT.default_translation || process.env.NEXT_PUBLIC_DEFAULT_TRANSLATION,
    };
    if (!payload.metadata) payload.metadata = {};
    for (const param in metadataDefaults) {
        if (!payload.metadata[param]) {
            payload.metadata[param] = metadataDefaults[param];
        }
    }

    // Determine what client is hitting this endpoint
    const client = integration ? 'integration' : await getClient(tokenId, payload.metadata.parent_host);
    const isFrontend = ['standalone', 'embedded'].includes(client);

    // Figure out stream mode
    if (payload.stream === undefined) {
        payload.stream = (!isFrontend && (AGENT.api_response_format === 'raw')) ? false : envBoolean(process.env.NEXT_PUBLIC_STREAM);
        if (payload.stream) {
            payload.stream_options = {
                include_usage: true,
            };
        }
    }

    // Figure out the format
    if (isFrontend) {
        payload.response_format = { type: 'html'};
    } else {
        if (!payload.response_format) {
            payload.response_format = (client === 'api') ? { type: AGENT.api_response_format } : { type: 'text' };
        }
        if (!payload.stream && (payload.response_format.type === 'raw')) {
            payload.response_format.type = 'json';
        }
    }

    debug(envBoolean(process.env.SHOULD_LOG), 'CLIENT', client);
    debug(envBoolean(process.env.SHOULD_LOG), 'FORMAT', payload.response_format.type);

    // Determine the model to query
    const MODEL = payload.model ? await getAgentModelByKey(payload.model) : await getAgentModelById(AGENT.model_id);
    if (!MODEL) {
        return get422Response({ success: false, errors: ['Invalid model.'] });
    }
    const PROVIDER = getModelProvider(MODEL);
    debug(envBoolean(process.env.SHOULD_LOG), 'MODEL', MODEL);

    // Normalize messages array
    if (payload.messages && (payload.messages.length > 0) && (payload.messages[0].parts)) {
        payload.messages = convertToModelMessages(payload.messages);
        debug(envBoolean(process.env.SHOULD_LOG), 'LAST MESSAGE', payload.messages[payload.messages.length-1]);
    }

    // Set the prompt and autodetect language if not set
    const prompt = payload.prompt ?? getMessageText(payload.messages[payload.messages.length-1]);
    debug(envBoolean(process.env.SHOULD_LOG), 'PROMPT', prompt);
    if (!payload.metadata.language) {
        payload.metadata.language = await detectLanguage(prompt) || AGENT.default_language || process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    }

    // Set the session ID if not API
    if (isFrontend && !payload.metadata.session) {
        payload.metadata.session = await getSessionValue('session_id');
    }

    // Log the session if a session ID is set
    if (payload.metadata.session) {
        waitUntil(logSession(payload.metadata.session, payload.metadata.language, payload.metadata.parent_url, payload.metadata.parent_host, payload.user));
    }
    debug(envBoolean(process.env.SHOULD_LOG), 'SESSION', payload.metadata.session ?? null);

    // Figure out the translation if configured
    const realTimeTranslation = shouldUseRealTimeTranslation(
        payload.metadata.language,
        process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
        AGENT.auto_translate,
        AGENT.auto_translate_languages
    );
    const translatedPrompt = realTimeTranslation ? await translateToLanguage(prompt, payload.metadata.language, process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE) : prompt;
    debug(envBoolean(process.env.SHOULD_LOG), 'TRANSLATED PROMPT', translatedPrompt);

    // Translate the messages if realtime translation is enabled
    if (realTimeTranslation && payload.messages) {
        payload.messages = await translateMessages(payload.messages, payload.metadata.language, process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE);
        debug(envBoolean(process.env.SHOULD_LOG), 'TRANSLATED MESSAGES', payload.messages);
    }

    // Set the conversation ID if not API
    if (isFrontend && !payload.metadata.conversation) {
        payload.metadata.conversation = await getSessionValue('conversation_id');
    }
    debug(envBoolean(process.env.SHOULD_LOG), 'CONVERSATION', payload.metadata.conversation ?? null);

    // Get the config ID
    const configId = await getConfigId(AGENT, MODEL, payload);

    // Log the new prompt and get the prompt ID
    const integrationId = integration ? integration.id : null;
    const promptId = await getPromptId(
        prompt,
        payload,
        AGENT,
        configId,
        realTimeTranslation,
        translatedPrompt,
        client,
        integrationId,
        tokenId
    );
    if (!promptId) {
        debug(true, 'PROMPT INSERT FAILED');
    }

    // Get the RAG snippets
    const ctx = envBoolean(process.env.NEXT_PUBLIC_RAG) ?
        await getContext(translatedPrompt, promptId, AGENT, payload.metadata.language, cacheTtl) :
        EMPTY_RAG_CONTEXT
    ;
    debug(envBoolean(process.env.SHOULD_LOG), 'CONTEXT', ctx);

    // Figure out if the passed messages array has a system prompt
    let messagesHaveSystemPrompt = false;
    if (payload.messages) {

        messagesHaveSystemPrompt = payload.messages.some(item => SYSTEM_ROLES.includes(item.role));

        // Filter out any system prompts if the agent has locked the system prompt
        if (messagesHaveSystemPrompt && AGENT.lock_system_prompt) {
            payload.messages = payload.messages.filter(item => !SYSTEM_ROLES.includes(item.role));
        }

    }

    // Generate the system prompt by replacing placeholder tokens
    const responseLanguage = realTimeTranslation ? process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE : payload.metadata.language;
    const CACHE_BUSTER = getPromptCacheBuster(AGENT, configId);
    const systemPrompt = (messagesHaveSystemPrompt && !AGENT.lock_system_prompt) ?
        null :
        await getSystemPrompt(AGENT, ctx.text, responseLanguage, payload.metadata.translation, CACHE_BUSTER)
    ;

    // Generate the message array
    let messages = [];
    if (payload.messages && (payload.messages.length >= 1)) {
        messages = payload.messages;
    } else {
        const numMemories = payload.metadata.anonymous ? 0 : (payload.metadata.max_memories ?? AGENT.max_memories ?? process.env.COMPLETION_API_MEMORIES);
        messages = await getMessages(
            systemPrompt,
            payload.metadata.session,
            payload.metadata.conversation,
            promptId,
            translatedPrompt,
            payload.metadata.shared_prompt ?? null,
            numMemories,
            realTimeTranslation
        );
    }
    debug(envBoolean(process.env.SHOULD_LOG), 'MESSAGES', messages);

    // Set the chat completion request body
    const completionQry = getCompletionQuery(
        payload,
        AGENT,
        PROVIDER,
        MODEL,
        systemPrompt,
        messages,
        PROVIDER.custom ?? {}
    );

    const responseParams = {
        id: promptId.toString(),
        sources: ctx.sources,
        object: 'chat.completion',
        created: Math.floor(new Date().getTime() / 1000),
        model: MODEL.key,
        stream: payload.stream,
        metadata: payload.metadata,
        user: payload.user,
        usage: {},
        timings: {},
        system_fingerprint: configId.toString(),
    };

    // If the Agent has any active CTAs, send down the counts & cta
    const ctas = await getCtas(AGENT.id, client, integrationId);
    debug(envBoolean(process.env.SHOULD_LOG), 'CTAs', ctas);
    if (ctas.length > 0) {
        responseParams.response_counts = await getResponseCounts(
            AGENT.id,
            payload.metadata?.conversation ?? null,
            payload.metadata?.session ?? null,
            payload.metadata?.device ?? null,
            payload.user ?? null
        );
        debug(envBoolean(process.env.SHOULD_LOG), 'RESPONSE COUNTS', responseParams.response_counts);
        responseParams.cta = getMessageCta(ctas, responseParams.response_counts, payload.metadata.language, !isFrontend);
    }

    // Set the response options
    const responseOptions = {
        status: 200,
        headers: {
            'Content-Type': `${CONTENT_TYPES[payload.response_format.type]}; charset=utf-8`,
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
        },
    };

    // Log the prompt response start time
    waitUntil(setCompletionStart(promptId));

    // Get the cached completion if it exists
    let cachedCompletion = await getCachedCompletion(
        cacheTtl,
        messages,
        AGENT.id,
        configId,
        payload.metadata.language,
        payload.metadata.translation
    );
    if (cachedCompletion) {
        cachedCompletion = transformCompletion(cachedCompletion, (payload.response_format.type === 'html'), AGENT.strip_markdown);
    }

    // Return a streaming response if streaming is enabled
    debug(envBoolean(process.env.SHOULD_LOG), 'STREAM', (payload.stream ? 'YES' : 'NO'));
    if (payload.stream) {
        return getStreamingResponse(
            isFrontend,
            completionQry,
            payload,
            promptId,
            prompt,
            messages,
            realTimeTranslation,
            responseParams,
            responseOptions,
            MODEL.fallback_model_id,
            cacheTtl,
            AGENT,
            configId,
            cachedCompletion,
            startTime
        );
    } else {
        return await getNonStreamingResponse(
            completionQry,
            payload,
            promptId,
            prompt,
            messages,
            realTimeTranslation,
            responseParams,
            responseOptions,
            MODEL.fallback_model_id,
            cachedCompletion,
            cacheTtl,
            AGENT,
            configId,
            startTime
        );
    }

}

// Get the current runtime client
async function getClient(tokenId, parent_host) {
    if (tokenId) {
        return 'api';
    } else if (parent_host) {
        return 'embedded';
    } else {
        return 'standalone';
    }
}

// Get snippets from vector DB API
async function getContext(prompt, promptId, agent, language, cacheTtl) {

    const metaFilter = await getMetaFilter(agent, language, agent.prioritize_team_corpus);
    debug(envBoolean(process.env.SHOULD_LOG), 'RAG FILTER', metaFilter);

    if (metaFilter) {

        const query = prompt.trim();
        const corpusId = agent.has_custom_corpus ? process.env.RAG_TEAM_PREFIX + agent.id : process.env.RAG_CORPUS_KEY;
        const apiKey = (agent.has_custom_corpus && agent.corpus_api_key) ? await decryptBackend(agent.corpus_api_key) : process.env.RAG_API_KEY;
        debug(envBoolean(process.env.SHOULD_LOG), 'RAG CORPUS', corpusId);

        let results = await searchCorpus(
            corpusId,
            apiKey,
            agent,
            query,
            metaFilter,
            parseInt(process.env.RAG_NUM_RESULTS) * 2,
            process.env.RAG_SCORE_THRESHOLD,
            process.env.RAG_LEXICAL_INTERPOLATION,
            cacheTtl
        );

        if (agent.prioritize_team_corpus) {

            const metaFilter2 = await getMetaFilter(agent, language);
            debug(envBoolean(process.env.SHOULD_LOG), 'RAG FILTER 2', metaFilter2);

            const results2 = await searchCorpus(
                corpusId,
                apiKey,
                agent,
                query,
                metaFilter2,
                parseInt(process.env.RAG_NUM_RESULTS) * 2,
                process.env.RAG_SCORE_THRESHOLD,
                process.env.RAG_LEXICAL_INTERPOLATION,
                cacheTtl
            );

            if (results2) {
                results = results ? results.concat(results2) : results2;
            }

        }

        if (results) {
            return prepareContext(results, prompt, promptId, agent);
        }

    }

    return EMPTY_RAG_CONTEXT;

}

// Get the meta filter used for RAG
async function getMetaFilter(agent, language, prioritizeTeamCycle = false) {

    let metaFilter = '';

    if (prioritizeTeamCycle) {
        metaFilter += `(doc.team = ${agent.team_id})`;

    } else {

        const teamId = (agent.use_team_corpus ? agent.team_id : null);
        const hasCustomSources = (
            (agent.categories?.length > 0) ||
            (agent.tags?.length > 0) ||
            (agent.collections?.length > 0) ||
            (agent.contributors?.length > 0) ||
            (agent.sources?.length > 0) ||
            agent.classification_id ||
            agent.use_team_corpus
        );

        metaFilter += '(';

        if (!agent.disable_community_corpus) {
            metaFilter += `((doc.is_approved = true) AND (doc.weight >= ${parseInt(process.env.NEXT_PUBLIC_RAG_MIN_WEIGHT)}))`;
        }

        if (hasCustomSources) {

            if (agent.sources?.length > 0) {
                const idStr = formatDocumentIds(agent.sources);
                metaFilter += prependMetaFilterCombinator(`(doc.id IN (${idStr}))`, metaFilter, 'OR');
            }

            const filters = ['categories', 'tags', 'collections', 'contributors'];
            for (const filter of filters) {
                if (agent[filter]?.length > 0) {
                    for (const id of agent[filter]) {
                        metaFilter += prependMetaFilterCombinator(`(${id} IN doc.${filter})`, metaFilter, 'OR');
                    }
                }
            }

            if (agent.classification_id) {
                metaFilter += prependMetaFilterCombinator(`(doc.classification = ${agent.classification_id})`, metaFilter, 'OR');
            }

            if (agent.prioritize_team_corpus) {
                metaFilter += ')';
                metaFilter += prependMetaFilterCombinator(`(doc.team != ${teamId})`, metaFilter, 'AND');
            } else {
                if (agent.use_team_corpus) {
                    metaFilter += prependMetaFilterCombinator(`(doc.team = ${teamId})`, metaFilter, 'OR');
                }
                metaFilter += ')';
            }

        } else {
            metaFilter += ')';
        }

    }

    metaFilter = metaFilter.replace('()', '');

    if (metaFilter.length > 0) {
        if (language === process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE) {
            metaFilter += ` AND (doc.lang = '${language}')`;
        }
        return metaFilter;
    } else {
        return null;
    }

}

function prepareContext(results, prompt, promptId, agent) {

    let ctx = EMPTY_RAG_CONTEXT;

    // Loop through returned results and generate prompt context + log snippets
    if (results) {

        let sources = [];
        debug(envBoolean(process.env.SHOULD_LOG), 'RAG RESULTS', results);
        results.forEach((result) => {
            try {
                const source = expandRagDocumentAttrs(result.document_id, result.document_metadata);
                if (shouldIncludeSource(source, agent)) {
                    ctx.text += '"' + source.title + '" by ' + source.authors.join(', ') + ':';
                    ctx.text += "\n" + decode(result.text) + "\n---\n";
                    waitUntil(insertPromptSource(promptId, source.id, result.text, result.score));
                    sources.push(source);
                    debug(envBoolean(process.env.SHOULD_LOG), 'INCLUDE CITATION', `${source.title} [${source.id}]`);
                } else {
                    debug(envBoolean(process.env.SHOULD_LOG), 'INCORRECT SOURCE', `${source.title} [${source.id}]`);
                }
            } catch (error) {
                debug(envBoolean(process.env.SHOULD_LOG), 'ERROR', error);
            }
        });
        debug(envBoolean(process.env.SHOULD_LOG), 'SOURCES', sources);

        ctx.sources = filterUniqueDocs(
            sources,
            parseInt(process.env.RAG_NUM_RESULTS)
        );

    }

    return ctx;

}

function getCompletionQuery(
    payload,
    agent,
    provider,
    model,
    systemPrompt,
    messages,
    customParams,
) {

    const maxOutputTokens = parseInt(payload.max_completion_tokens || agent.model_max_tokens || model.max_tokens || process.env.COMPLETION_API_DEFAULT_MAX_TOKENS);

    let providerOptions = {
        openai: {},
        xai: {},
        anthropic: {},
        groq: {},
        google: { responseModalities: ['TEXT'] },
    };

    // Add reasoning effort params
    if (model.supports_reasoning_effort) {

        const reasoningEffort = payload.reasoning_effort ?? agent.model_reasoning_effort ?? 'medium';

        // OpenAI
        providerOptions.openai.reasoningEffort = reasoningEffort;
        providerOptions.openai.reasoningSummary = 'auto';

        // XAI
        providerOptions.xai.reasoningEffort = reasoningEffort;

        // Groq
        providerOptions.groq.reasoningEffort = reasoningEffort;
        providerOptions.groq.reasoningFormat = 'parsed';

        // Anthropic
        providerOptions.anthropic.sendReasoning = true;
        providerOptions.anthropic.thinking = {
            type: 'enabled',
            budgetTokens: maxOutputTokens
        };

        // Google
        providerOptions.google.thinkingConfig = {
            thinkingBudget: maxOutputTokens,
            includeThoughts: true,
        };

    }

    // Add verbosity params
    if (model.supports_verbosity) {
        providerOptions.openai.textVerbosity = payload.verbosity ?? agent.model_verbosity ?? 'medium';
    }

    let completionTimeout = parseInt(process.env.COMPLETION_TIMEOUT);
    if (model.supports_reasoning_effort && ['medium', 'high'].includes(agent.reasoning_effort)) {
        completionTimeout = completionTimeout * 2;
    }
    let completionQry = {
        model: getCompletionQueryModel(model, provider),
        messages: messages,
        maxOutputTokens: maxOutputTokens,
        providerOptions: providerOptions,
        abortSignal: AbortSignal.timeout(completionTimeout),
    };

    // Set system prompt
    if (systemPrompt) {
        completionQry.system = systemPrompt;
    }

    // Set model params if they're supported
    for (const param in MODEL_PARAM_DEFAULTS) {
        const snakeParam = camelToSnake(param);
        if (model[`supports_${snakeParam}`]) {
            completionQry[param] = parseFloat(payload[snakeParam] ?? agent[`model_${snakeParam}`] ?? paramDefaults[param]);
        }
    }

    const stopSeq = payload.stop || model.stop_sequence || process.env.COMPLETION_STOP_SEQUENCE;
    if (stopSeq && (stopSeq.length > 0)) {
        completionQry.stop = stopSeq;
    }

    completionQry = {...completionQry, ...customParams}

    debug(envBoolean(process.env.SHOULD_LOG), 'COMPLETION QUERY', completionQry);

    return completionQry;

}

// Get or create the agent config
async function getConfigId(agent, model, payload) {

    // Figure out system prompt
    let systemPrompt = agent.model_system_prompt;
    if (payload.messages) {
        payload.messages.forEach(function(message) {
            if (['system', 'developer'].includes(message.role)) {
                systemPrompt = message.content;
            }
        });
    }

    // See if the agent config being used already exists
    const configParams = {
        max_tokens: payload.max_completion_tokens ?? agent.model_max_tokens ?? MODEL_PARAM_DEFAULTS.maxTokens,
        temperature: payload.temperature ?? agent.model_temperature ?? MODEL_PARAM_DEFAULTS.temperature,
        top_p: payload.top_p ?? agent.model_top_p ?? MODEL_PARAM_DEFAULTS.topP,
        frequency_penalty: payload.frequency_penalty ?? agent.model_frequency_penalty ?? MODEL_PARAM_DEFAULTS.frequencyPenalty,
        presence_penalty: payload.presence_penalty ?? agent.model_presence_penalty ?? MODEL_PARAM_DEFAULTS.presencePenalty,
        reasoning_effort: payload.reasoning_effort ?? agent.model_reasoning_effort ?? null,
        verbosity: payload.verbosity ?? agent.model_verbosity ?? null,
        model_id: model.id,
        agent_id: agent.id,
        system_prompt: systemPrompt,
        model_key: model.provider_model,
    };
    debug(envBoolean(process.env.SHOULD_LOG), 'CONFIG PARAMS', configParams);
    let config = await getAgentConfig(configParams);
    debug(envBoolean(process.env.SHOULD_LOG), 'CONFIG', config);

    // If the agent config being used doesn't exist, insert a new one
    if (!config) {
        config = await insertAgentConfig(configParams);
    }

    return config.id;

}

// Insert the new prompt log and return the prompt ID
async function getPromptId(
    prompt,
    payload,
    agent,
    configId,
    realTimeTranslation,
    translatedPrompt,
    client,
    integrationId,
    tokenId
) {
    const record = await insertPrompt({
        language: payload.metadata.language,
        translation: payload.metadata.translation,
        prompt: prompt,
        config_id: configId,
        user_id: payload.user,
        device_id: payload.metadata.device,
        session_id: payload.metadata.session,
        conversation_id: payload.metadata.conversation,
        translated_prompt: realTimeTranslation ? translatedPrompt : null,
        client: client,
        agent_integration_id: integrationId,
        agent_id: agent.id,
        agent_token_id: tokenId,
    });
    return record.id;
}

// Replace system prompt placeholder tokens and return interpolated string
async function getSystemPrompt(agent, ctx, language, translation, cacheBuster) {

    // Initialize prompt pieces
    let systemPrompt = agent.model_system_prompt?.trim();
    let contextPrompt = agent.model_context_prompt?.trim();

    // Loop up to parent prompts to prepend them
    systemPrompt = await prependParentPrompt(systemPrompt, agent);

    // Prepend prompt cache buster
    systemPrompt = prependPromptCacheBuster(systemPrompt, cacheBuster);

    // Add hard-coded system instructions
    if (agent.strip_markdown) {
        systemPrompt += STRIP_MARKDOWN_INSTRUCTIONS;
    }

    // Replace language token
    const languages = JSON.parse(process.env.NEXT_PUBLIC_LANGUAGES);
    const translations = JSON.parse(process.env.NEXT_PUBLIC_TRANSLATIONS);
    contextPrompt = replaceTokens(
        contextPrompt,
        {
            language: languages[language],
            translation: translations[translation],
            passages: ctx,
        }
    );

    // Append dynamic context prompt to system prompt
    systemPrompt += "\n\n" + contextPrompt;

    debug(envBoolean(process.env.SHOULD_LOG), 'SYSTEM PROMPT',systemPrompt);
    return systemPrompt;

}

async function prependParentPrompt(systemPrompt, agent) {
    if (agent.parent_id) {
        const parentAgent = await getExtensibleAgentById(agent.parent_id, agent.team_id);
        if (parentAgent) {
            return await prependParentPrompt((parentAgent.model_system_prompt ?? '') + "\n\n" + (systemPrompt ?? ''), parentAgent);
        }
    }
    return systemPrompt;
}

function getPromptCacheBuster(agent, configId) {
    return `[SYSTEM PROMPT VERSION: ${agent.team_id}-${agent.id}-${configId}]`;
}

function prependPromptCacheBuster(prompt, cacheBuster) {
    return `${cacheBuster}\n\n${prompt}`;
}

// Generate the messages array
async function getMessages(
    systemPrompt,
    sessionId,
    conversationId,
    promptId,
    prompt,
    sharedPromptId,
    numMemories,
    realTimeTranslation
) {

    // Initialize the messages array
    let messages = [];

    // Unless this is not an anonymous query, add the last 3 exchanges to messages
    debug(envBoolean(process.env.SHOULD_LOG), 'NUM MEMORIES', numMemories);
    if (numMemories) {
        const prompts = await getRecentPrompts(
            promptId,
            sharedPromptId,
            conversationId,
            sessionId,
            realTimeTranslation,
            numMemories
        );
        prompts.reverse().forEach((prompt) => {
            if (prompt.prompt) {
                messages.push(createMessageObject('user', prompt.prompt));
            }
            if (prompt.response) {
                messages.push(createMessageObject('assistant', prompt.response));
            }
        });
    }

    // Add the most recent prompt to messages
    messages.push(createMessageObject('user', prompt));
    debug(envBoolean(process.env.SHOULD_LOG), 'MESSAGES', messages);

    return messages;

}

export function createMessageObject(role, content) {
    return {
        role: role,
        content: content,
    };
}

async function translateMessages(messages, from, to) {
    const translatedMessages = [];
    for (const message of messages) {
        message.content[0].text = await translateToLanguage(message.content[0].text, from, to);
        translatedMessages.push(message);
    }
    return translatedMessages;
}

// Log the session in the DB and return the session ID
export const logSession = async function(sessionId, language, parentUrl, parentHost, userId) {

    // See if the session already exists
    // debug(envBoolean(process.env.SHOULD_LOG), 'DB: SESSION');
    const session = await getSessionById(sessionId);

    // If the session doesn't exist, insert a new one
    if (!session) {

        const headersList = await headers();
        await insertSession({
            id: sessionId,
            language: language,
            user_agent: headersList.get('user-agent'),
            host: headersList.get('host'),
            ip_country: headersList.get('x-vercel-ip-country'),
            ip_region: decodeURIComponent(headersList.get('x-vercel-ip-country-region')),
            ip_city: decodeURIComponent(headersList.get('x-vercel-ip-city')),
            ip_latitude: headersList.get('x-vercel-ip-latitude'),
            ip_longitude: headersList.get('x-vercel-ip-longitude'),
            ip_timezone: headersList.get('x-vercel-ip-timezone'),
            parent_url: parentUrl,
            parent_host: parentHost,
            user_id: userId,
        })

    }

}

// Log the time the prompt response starts
export const setCompletionStart = async function(promptId) {
    debug(envBoolean(process.env.SHOULD_LOG), `Prompt #${promptId} completion started`);
    return updatePrompt(
        promptId,
        {},
        'response_started_at'
    );
}
