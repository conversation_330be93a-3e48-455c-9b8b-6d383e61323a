import showdown from 'showdown';

showdown.setOption('tables', true);
const converter = new showdown.Converter();

export function transformCompletion(line, convertToHtml = true, stripMarkdown = false) {
    if (!line) return '';
    line = line.trim();
    if (convertToHtml || stripMarkdown) {
        line = markdownToHtml(line);
        if (stripMarkdown) {
            line = line.replace(/<[^>]*>/g, '');
        }
    }
    return line;
}

export function markdownToHtml(markdown) {
    return converter.makeHtml(markdown);
}

export function htmlToMarkdown(html, parser) {
    return converter.makeMarkdown(html, parser);
}
