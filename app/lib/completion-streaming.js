import { translateToLanguage } from './language';
import {
    transformCompletion,
    htmlToMarkdown,
} from './format';
import {
    debug,
    getMessageText,
    envBoolean,
} from './helpers';
import {
    simulateReadableStream,
    smoothStream,
    streamText
} from 'ai';
import { get408Response } from './response';
import { MockLanguageModelV2 } from 'ai/test';
import {
    setCompletionFinish,
    setCompletionCache,
    decorateCompletionParams,
    parseCompletion,
    getFallbackCompletionQuery,
    REASONING_START_SEQUENCE,
    REASONING_END_SEQUENCE,
} from './completion-utils';
import { waitUntil } from '@vercel/functions';
import { JSDOM } from 'jsdom';

const COMPLETION_HEADER = 'x-completion';

export async function getStreamingResponse(
    isFrontend,
    completionQry,
    payload,
    promptId,
    prompt,
    messages,
    realTimeTranslation,
    responseParams,
    responseOptions,
    fallbackModelId,
    cacheTtl,
    agent,
    configId,
    cachedCompletion,
    startTime
) {

    let tmpText = '';
    let fullText = '';
    let fullTextTranslated = '';
    let reasoning = '';
    let thinking = false;
    const transformStream = () => options => new TransformStream({

        async transform(chunk, controller) {

            // debug(envBoolean(process.env.SHOULD_LOG), chunk);
            if (chunk.type === 'text-delta') {

                // TODO: OpenAI reasoning models don't seem to pass back reasoning summaries ...
                if (!thinking && (chunk.text.indexOf(REASONING_START_SEQUENCE) !== -1)) {
                    thinking = true;
                }

                if (thinking) {

                    let endThinkingPos = chunk.text.indexOf(REASONING_END_SEQUENCE);
                    let beforeEndThink = null;
                    let afterEndThink = null;

                    if (endThinkingPos !== -1) {
                        beforeEndThink = chunk.text.slice(0, endThinkingPos);
                        afterEndThink = chunk.text.slice(endThinkingPos + REASONING_END_SEQUENCE.length);
                        reasoning += beforeEndThink;
                        chunk.text = afterEndThink;
                        thinking = false;
                    } else {
                        reasoning += chunk.text;
                    }

                }

                if (!thinking) {

                    fullText += chunk.text;
                    if (realTimeTranslation) {

                        tmpText += chunk.text;
                        let lines = tmpText.split(/\n/).map(line => line.trim());
                        tmpText = lines.pop();

                        for (let line of lines) {
                            if (line.length === 0) continue;
                            try {
                                let translatedChunk = await translateToLanguage(
                                    line,
                                    process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
                                    payload.metadata.language
                                );
                                fullTextTranslated += translatedChunk;
                                controller.enqueue({
                                    ...chunk,
                                    text: transformCompletion(translatedChunk, (payload.response_format.type === 'html'), agent.strip_markdown),
                                });
                            } catch (e) {
                                debug(envBoolean(process.env.SHOULD_LOG), 'ERROR', e);
                            }
                        }

                    } else {
                        controller.enqueue(chunk);
                    }

                }

            } else {
                controller.enqueue(chunk);
            }

        },

    });

    // No need to transform cached completions
    let transforms = [];
    if (!cachedCompletion) {
        transforms.push(transformStream());
    }
    transforms.push(smoothStream());

    const streamingCompletionSettings = {
        ...completionQry,
        ...{
            onFinish: (result) => {

                debug(envBoolean(process.env.SHOULD_LOG), 'STREAM FINISH', result);

                let response = result.text;
                let translatedResponse = null;
                if (cachedCompletion) {
                    response = htmlToMarkdown(cachedCompletion, new JSDOM().window.document);
                } else if (realTimeTranslation) {
                    translatedResponse = fullText;
                }

                if (!cachedCompletion && cacheTtl) {
                    setCompletionCache(
                        cacheTtl,
                        response,
                        messages,
                        agent.id,
                        configId,
                        payload.metadata.language,
                        payload.metadata.translation
                    );
                }

                if (!isFrontend) {
                    waitUntil(setCompletionFinish(
                        promptId,
                        prompt,
                        messages,
                        response,
                        reasoning,
                        realTimeTranslation,
                        payload.metadata.language,
                        result.totalUsage,
                        !!cachedCompletion,
                        translatedResponse
                    ));
                }

            },
            onError: (error) => {
                debug(true, 'ERROR', error);
                return {
                    errorCode: 'STREAM_ERROR',
                    message: 'An error occurred while processing your request.',
                };
            },
            onAbort: async (result) => {
                debug(true, 'STREAM_ABORTED', result);
                return {
                    errorCode: 'STREAM_ABORTED',
                    message: 'Stream aborted.',
                };
            },
            experimental_transform: transforms,
        }
    };

    const completion = await getStreamingCompletion(streamingCompletionSettings, fallbackModelId, cachedCompletion);
    if (!completion) {
        return get408Response();
    }
    debug(envBoolean(process.env.SHOULD_LOG), 'RESPONSE', JSON.stringify(completion, 4));

    responseParams.cached = process.env.CACHED_RESPONSE;
    if (payload.response_format.type === 'raw') {
        return getRawStreamingResponse(completion, responseParams, responseOptions);

    } else {

        const completionData = decorateCompletionParams(responseParams, messages, null, null, startTime);

        if (isFrontend) {

            let streaming = false;
            return completion.toUIMessageStreamResponse({

                ...responseOptions,

                messageMetadata: ({ part }) => {

                    // Send the metadata we know up front
                    if (part.type === 'start') {
                        return {
                            ...completionData,
                            metadata: {
                                ...(completionData.metadata ?? {}),
                                conversation: payload.metadata?.conversation ?? null,
                            },
                            prompt: prompt,
                        };
                    }

                    // Send a realtime update re: whether text is actually streaming (vs. just reasoning)
                    if (part.type === 'text-delta' && !streaming) {
                        if (part.text.trim().length > 0) {
                            streaming = true;
                            return { streaming: streaming };
                        }
                    }

                    // Tack on the usage info at the end
                    if (part.type === 'finish') {
                        return {
                            usage: part.totalUsage,
                            streaming: streaming,
                        };
                    }

                },

                onFinish: (result) => {

                    debug(envBoolean(process.env.SHOULD_LOG), 'MESSAGE FINISH', result.responseMessage);
                    const completion = getMessageText(result.responseMessage);
                    let { response, reasoning } = parseCompletion(completion);

                    let translatedResponse = null;
                    if (cachedCompletion) {
                        response = htmlToMarkdown(cachedCompletion, new JSDOM().window.document);
                    } else if (realTimeTranslation) {
                        translatedResponse = fullText;
                        response = fullTextTranslated;
                    }

                    waitUntil(setCompletionFinish(
                        promptId,
                        prompt,
                        messages,
                        response,
                        reasoning,
                        realTimeTranslation,
                        payload.metadata.language,
                        result.responseMessage.metadata.usage,
                        !!cachedCompletion,
                        translatedResponse
                    ));

                },

            });

        } else {
            responseOptions.headers[COMPLETION_HEADER] = encodeURIComponent(JSON.stringify(completionData));
            return completion.toTextStreamResponse(responseOptions);
        }

    }

}

async function getStreamingCompletion(completionQry, fallbackModelId, cachedCompletion) {
    try {
        return getTextStream(completionQry, cachedCompletion);
    } catch(e) {
        debug(envBoolean(process.env.SHOULD_LOG), 'ERROR', e);
        if (fallbackModelId) {
            return getTextStream(await getFallbackCompletionQuery(completionQry, fallbackModelId), cachedCompletion);
        } else {
            return false;
        }
    }
}

function getTextStream(completionQry, cachedCompletion) {
    if (cachedCompletion) {
        return streamText({
            ...completionQry,
            model: new MockLanguageModelV2({
                doStream: async () => ({
                    stream: simulateReadableStream({
                        chunks: getSimulatedCompletionChunks(cachedCompletion),
                    }),
                }),
            })
        });
    } else {
        return streamText(completionQry);
    }
}

function getRawStreamingResponse(completion, responseParams, responseOptions) {

    // Create OpenAI-compatible SSE stream
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
        async start(controller) {

            // Send initial chunk with role
            const initialChunk = getOpenAIResponseChunk(responseParams, { role: 'assistant', content: '' });
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialChunk)}\n\n`));

            try {

                // Stream the text deltas
                for await (const textPart of completion.textStream) {
                    const chunk = getOpenAIResponseChunk(responseParams,{ content: textPart });
                    controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`));
                }

                // Send final chunk with finish_reason
                const finalChunk = getOpenAIResponseChunk(responseParams, {}, 'stop');
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
                controller.enqueue(encoder.encode(`data: [DONE]\n\n`));

            } catch (error) {
                debug(envBoolean(process.env.SHOULD_LOG), 'STREAMING ERROR', error);
                controller.error(error);
            } finally {
                controller.close();
            }

        }
    });

    return new Response(stream, responseOptions);

}

function getSimulatedCompletionChunks(text) {

    let chunks = [
        { type: 'text-start', id: 'text-1' }
    ];

    // const lines = text.split("\n").map((line, index) => ({
    //     type: 'text-delta',
    //     id: 'text-1',
    //     delta: line + "\n",
    // }));
    //
    // for (const line of lines) {
    //     chunks.push(line);
    // }

    chunks.push({
        type: 'text-delta',
        id: 'text-1',
        delta: text,
    });

    chunks.push({ type: 'text-end', id: 'text-1' });
    chunks.push({
        type: 'finish',
        finishReason: 'stop',
        logprobs: undefined,
        usage: { inputTokens: 0, outputTokens: 0, totalTokens: 0 },
        totalUsage: { inputTokens: 0, outputTokens: 0, totalTokens: 0 }
    });

    return chunks;

}

function getOpenAIResponseChunk(responseParams, delta, finishReason = null) {
    return {
        id: responseParams.id,
        object: `chat.completion.chunk`,
        created: responseParams.created,
        model: responseParams.model,
        system_fingerprint: responseParams.system_fingerprint,
        choices: [{
            index: 0,
            delta: delta,
            logprobs: null,
            finish_reason: finishReason
        }]
    };
}
