/**
 * @typedef {import('livekit-client').TranscriptionSegment} TranscriptionSegment
 */

/**
 * @typedef {Object} CombinedTranscription
 * @property {'assistant' | 'user'} role - The role of the speaker
 * @property {number} receivedAtMediaTimestamp - Media timestamp when received
 * @property {number} receivedAt - Timestamp when received
 * @property {string} text - The transcription text (from TranscriptionSegment)
 * @property {string} id - The segment ID (from TranscriptionSegment)
 * @property {number} startTime - Start time of the segment (from TranscriptionSegment)
 * @property {number} endTime - End time of the segment (from TranscriptionSegment)
 * @property {boolean} final - Whether the segment is final (from TranscriptionSegment)
 */

/**
 * @typedef {'dark' | 'light' | 'system'} ThemeMode
 */

/**
 * @typedef {Object} AppConfig
 * @property {string} pageTitle - The page title
 * @property {string} pageDescription - The page description
 * @property {string} companyName - The company name
 * @property {boolean} supportsChatInput - Whether chat input is supported
 * @property {boolean} supportsVideoInput - Whether video input is supported
 * @property {boolean} supportsScreenShare - Whether screen sharing is supported
 * @property {boolean} isPreConnectBufferEnabled - Whether pre-connect buffer is enabled
 * @property {string} logo - The logo URL
 * @property {string} startButtonText - The start button text
 * @property {string} [accent] - Optional accent color
 * @property {string} [logoDark] - Optional dark mode logo URL
 * @property {string} [accentDark] - Optional dark mode accent color
 */

/**
 * @typedef {Object} SandboxConfigValue
 * @property {'string' | 'number' | 'boolean'} type - The type of the value
 * @property {string | number | boolean} value - The actual value
 */

/**
 * @typedef {Object.<string, SandboxConfigValue | null>} SandboxConfig
 */

/**
 * @typedef {Object} EmbedErrorDetails
 * @property {React.ReactNode} title - The error title
 * @property {React.ReactNode} description - The error description
 */
