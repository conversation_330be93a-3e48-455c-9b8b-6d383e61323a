"use strict";module.exports = validate20;module.exports.default = validate20;const schema22 = {"type":"object","properties":{"query":{"type":"string","nullable":false},"prompt_id":{"type":"string","nullable":true},"limit":{"type":"integer","nullable":true,"minimum":1,"maximum":100},"filters":{"type":"object","properties":{"model":{"type":"string","nullable":true},"ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"types":{"type":"array","nullable":true,"items":{"type":"string"}},"languages":{"type":"array","nullable":true,"items":{"type":"string"}},"collection_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"contributor_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"category_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"classification_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"external_ids":{"type":"array","nullable":true,"items":{"type":"string"}},"team_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"user_ids":{"type":"array","nullable":true,"items":{"type":"integer"}},"is_locked":{"type":"boolean","nullable":true},"min_weight":{"type":"integer","nullable":true},"max_weight":{"type":"integer","nullable":true},"min_rating":{"type":"integer","nullable":true},"max_rating":{"type":"integer","nullable":true},"min_num_views":{"type":"integer","nullable":true},"max_num_views":{"type":"integer","nullable":true},"min_num_impressions":{"type":"integer","nullable":true},"max_num_impressions":{"type":"integer","nullable":true},"min_num_referrals":{"type":"integer","nullable":true},"max_num_referrals":{"type":"integer","nullable":true},"min_published_on":{"type":"string","nullable":true},"max_published_on":{"type":"string","nullable":true},"min_created_at":{"type":"string","nullable":true},"max_created_at":{"type":"string","nullable":true},"min_updated_at":{"type":"string","nullable":true},"max_updated_at":{"type":"string","nullable":true}}}},"required":["query"],"additionalProperties":false};function validate20(data, {instancePath="", parentData, parentDataProperty, rootData=data}={}){let vErrors = null;let errors = 0;if(errors === 0){if(data && typeof data == "object" && !Array.isArray(data)){let missing0;if((data.query === undefined) && (missing0 = "query")){validate20.errors = [{instancePath,schemaPath:"#/required",keyword:"required",params:{missingProperty: missing0},message:"must have required property '"+missing0+"'"}];return false;}else {const _errs1 = errors;for(const key0 in data){if(!((((key0 === "query") || (key0 === "prompt_id")) || (key0 === "limit")) || (key0 === "filters"))){validate20.errors = [{instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty: key0},message:"must NOT have additional properties"}];return false;break;}}if(_errs1 === errors){if(data.query !== undefined){const _errs2 = errors;if(typeof data.query !== "string"){validate20.errors = [{instancePath:instancePath+"/query",schemaPath:"#/properties/query/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid0 = _errs2 === errors;}else {var valid0 = true;}if(valid0){if(data.prompt_id !== undefined){let data1 = data.prompt_id;const _errs5 = errors;if((typeof data1 !== "string") && (data1 !== null)){validate20.errors = [{instancePath:instancePath+"/prompt_id",schemaPath:"#/properties/prompt_id/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid0 = _errs5 === errors;}else {var valid0 = true;}if(valid0){if(data.limit !== undefined){let data2 = data.limit;const _errs8 = errors;if((!(((typeof data2 == "number") && (!(data2 % 1) && !isNaN(data2))) && (isFinite(data2)))) && (data2 !== null)){validate20.errors = [{instancePath:instancePath+"/limit",schemaPath:"#/properties/limit/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}if(errors === _errs8){if((typeof data2 == "number") && (isFinite(data2))){if(data2 > 100 || isNaN(data2)){validate20.errors = [{instancePath:instancePath+"/limit",schemaPath:"#/properties/limit/maximum",keyword:"maximum",params:{comparison: "<=", limit: 100},message:"must be <= 100"}];return false;}else {if(data2 < 1 || isNaN(data2)){validate20.errors = [{instancePath:instancePath+"/limit",schemaPath:"#/properties/limit/minimum",keyword:"minimum",params:{comparison: ">=", limit: 1},message:"must be >= 1"}];return false;}}}}var valid0 = _errs8 === errors;}else {var valid0 = true;}if(valid0){if(data.filters !== undefined){let data3 = data.filters;const _errs11 = errors;if(errors === _errs11){if(data3 && typeof data3 == "object" && !Array.isArray(data3)){if(data3.model !== undefined){let data4 = data3.model;const _errs13 = errors;if((typeof data4 !== "string") && (data4 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/model",schemaPath:"#/properties/filters/properties/model/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs13 === errors;}else {var valid1 = true;}if(valid1){if(data3.ids !== undefined){let data5 = data3.ids;const _errs16 = errors;if((!(Array.isArray(data5))) && (data5 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/ids",schemaPath:"#/properties/filters/properties/ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs16){if(Array.isArray(data5)){var valid2 = true;const len0 = data5.length;for(let i0=0; i0<len0; i0++){let data6 = data5[i0];const _errs19 = errors;if(!(((typeof data6 == "number") && (!(data6 % 1) && !isNaN(data6))) && (isFinite(data6)))){validate20.errors = [{instancePath:instancePath+"/filters/ids/" + i0,schemaPath:"#/properties/filters/properties/ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid2 = _errs19 === errors;if(!valid2){break;}}}}var valid1 = _errs16 === errors;}else {var valid1 = true;}if(valid1){if(data3.types !== undefined){let data7 = data3.types;const _errs21 = errors;if((!(Array.isArray(data7))) && (data7 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/types",schemaPath:"#/properties/filters/properties/types/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs21){if(Array.isArray(data7)){var valid3 = true;const len1 = data7.length;for(let i1=0; i1<len1; i1++){const _errs24 = errors;if(typeof data7[i1] !== "string"){validate20.errors = [{instancePath:instancePath+"/filters/types/" + i1,schemaPath:"#/properties/filters/properties/types/items/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid3 = _errs24 === errors;if(!valid3){break;}}}}var valid1 = _errs21 === errors;}else {var valid1 = true;}if(valid1){if(data3.languages !== undefined){let data9 = data3.languages;const _errs26 = errors;if((!(Array.isArray(data9))) && (data9 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/languages",schemaPath:"#/properties/filters/properties/languages/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs26){if(Array.isArray(data9)){var valid4 = true;const len2 = data9.length;for(let i2=0; i2<len2; i2++){const _errs29 = errors;if(typeof data9[i2] !== "string"){validate20.errors = [{instancePath:instancePath+"/filters/languages/" + i2,schemaPath:"#/properties/filters/properties/languages/items/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid4 = _errs29 === errors;if(!valid4){break;}}}}var valid1 = _errs26 === errors;}else {var valid1 = true;}if(valid1){if(data3.collection_ids !== undefined){let data11 = data3.collection_ids;const _errs31 = errors;if((!(Array.isArray(data11))) && (data11 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/collection_ids",schemaPath:"#/properties/filters/properties/collection_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs31){if(Array.isArray(data11)){var valid5 = true;const len3 = data11.length;for(let i3=0; i3<len3; i3++){let data12 = data11[i3];const _errs34 = errors;if(!(((typeof data12 == "number") && (!(data12 % 1) && !isNaN(data12))) && (isFinite(data12)))){validate20.errors = [{instancePath:instancePath+"/filters/collection_ids/" + i3,schemaPath:"#/properties/filters/properties/collection_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid5 = _errs34 === errors;if(!valid5){break;}}}}var valid1 = _errs31 === errors;}else {var valid1 = true;}if(valid1){if(data3.contributor_ids !== undefined){let data13 = data3.contributor_ids;const _errs36 = errors;if((!(Array.isArray(data13))) && (data13 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/contributor_ids",schemaPath:"#/properties/filters/properties/contributor_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs36){if(Array.isArray(data13)){var valid6 = true;const len4 = data13.length;for(let i4=0; i4<len4; i4++){let data14 = data13[i4];const _errs39 = errors;if(!(((typeof data14 == "number") && (!(data14 % 1) && !isNaN(data14))) && (isFinite(data14)))){validate20.errors = [{instancePath:instancePath+"/filters/contributor_ids/" + i4,schemaPath:"#/properties/filters/properties/contributor_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid6 = _errs39 === errors;if(!valid6){break;}}}}var valid1 = _errs36 === errors;}else {var valid1 = true;}if(valid1){if(data3.category_ids !== undefined){let data15 = data3.category_ids;const _errs41 = errors;if((!(Array.isArray(data15))) && (data15 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/category_ids",schemaPath:"#/properties/filters/properties/category_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs41){if(Array.isArray(data15)){var valid7 = true;const len5 = data15.length;for(let i5=0; i5<len5; i5++){let data16 = data15[i5];const _errs44 = errors;if(!(((typeof data16 == "number") && (!(data16 % 1) && !isNaN(data16))) && (isFinite(data16)))){validate20.errors = [{instancePath:instancePath+"/filters/category_ids/" + i5,schemaPath:"#/properties/filters/properties/category_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid7 = _errs44 === errors;if(!valid7){break;}}}}var valid1 = _errs41 === errors;}else {var valid1 = true;}if(valid1){if(data3.classification_ids !== undefined){let data17 = data3.classification_ids;const _errs46 = errors;if((!(Array.isArray(data17))) && (data17 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/classification_ids",schemaPath:"#/properties/filters/properties/classification_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs46){if(Array.isArray(data17)){var valid8 = true;const len6 = data17.length;for(let i6=0; i6<len6; i6++){let data18 = data17[i6];const _errs49 = errors;if(!(((typeof data18 == "number") && (!(data18 % 1) && !isNaN(data18))) && (isFinite(data18)))){validate20.errors = [{instancePath:instancePath+"/filters/classification_ids/" + i6,schemaPath:"#/properties/filters/properties/classification_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid8 = _errs49 === errors;if(!valid8){break;}}}}var valid1 = _errs46 === errors;}else {var valid1 = true;}if(valid1){if(data3.external_ids !== undefined){let data19 = data3.external_ids;const _errs51 = errors;if((!(Array.isArray(data19))) && (data19 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/external_ids",schemaPath:"#/properties/filters/properties/external_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs51){if(Array.isArray(data19)){var valid9 = true;const len7 = data19.length;for(let i7=0; i7<len7; i7++){const _errs54 = errors;if(typeof data19[i7] !== "string"){validate20.errors = [{instancePath:instancePath+"/filters/external_ids/" + i7,schemaPath:"#/properties/filters/properties/external_ids/items/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid9 = _errs54 === errors;if(!valid9){break;}}}}var valid1 = _errs51 === errors;}else {var valid1 = true;}if(valid1){if(data3.team_ids !== undefined){let data21 = data3.team_ids;const _errs56 = errors;if((!(Array.isArray(data21))) && (data21 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/team_ids",schemaPath:"#/properties/filters/properties/team_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs56){if(Array.isArray(data21)){var valid10 = true;const len8 = data21.length;for(let i8=0; i8<len8; i8++){let data22 = data21[i8];const _errs59 = errors;if(!(((typeof data22 == "number") && (!(data22 % 1) && !isNaN(data22))) && (isFinite(data22)))){validate20.errors = [{instancePath:instancePath+"/filters/team_ids/" + i8,schemaPath:"#/properties/filters/properties/team_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid10 = _errs59 === errors;if(!valid10){break;}}}}var valid1 = _errs56 === errors;}else {var valid1 = true;}if(valid1){if(data3.user_ids !== undefined){let data23 = data3.user_ids;const _errs61 = errors;if((!(Array.isArray(data23))) && (data23 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/user_ids",schemaPath:"#/properties/filters/properties/user_ids/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}if(errors === _errs61){if(Array.isArray(data23)){var valid11 = true;const len9 = data23.length;for(let i9=0; i9<len9; i9++){let data24 = data23[i9];const _errs64 = errors;if(!(((typeof data24 == "number") && (!(data24 % 1) && !isNaN(data24))) && (isFinite(data24)))){validate20.errors = [{instancePath:instancePath+"/filters/user_ids/" + i9,schemaPath:"#/properties/filters/properties/user_ids/items/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid11 = _errs64 === errors;if(!valid11){break;}}}}var valid1 = _errs61 === errors;}else {var valid1 = true;}if(valid1){if(data3.is_locked !== undefined){let data25 = data3.is_locked;const _errs66 = errors;if((typeof data25 !== "boolean") && (data25 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/is_locked",schemaPath:"#/properties/filters/properties/is_locked/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];return false;}var valid1 = _errs66 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_weight !== undefined){let data26 = data3.min_weight;const _errs69 = errors;if((!(((typeof data26 == "number") && (!(data26 % 1) && !isNaN(data26))) && (isFinite(data26)))) && (data26 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_weight",schemaPath:"#/properties/filters/properties/min_weight/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs69 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_weight !== undefined){let data27 = data3.max_weight;const _errs72 = errors;if((!(((typeof data27 == "number") && (!(data27 % 1) && !isNaN(data27))) && (isFinite(data27)))) && (data27 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_weight",schemaPath:"#/properties/filters/properties/max_weight/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs72 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_rating !== undefined){let data28 = data3.min_rating;const _errs75 = errors;if((!(((typeof data28 == "number") && (!(data28 % 1) && !isNaN(data28))) && (isFinite(data28)))) && (data28 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_rating",schemaPath:"#/properties/filters/properties/min_rating/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs75 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_rating !== undefined){let data29 = data3.max_rating;const _errs78 = errors;if((!(((typeof data29 == "number") && (!(data29 % 1) && !isNaN(data29))) && (isFinite(data29)))) && (data29 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_rating",schemaPath:"#/properties/filters/properties/max_rating/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs78 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_num_views !== undefined){let data30 = data3.min_num_views;const _errs81 = errors;if((!(((typeof data30 == "number") && (!(data30 % 1) && !isNaN(data30))) && (isFinite(data30)))) && (data30 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_num_views",schemaPath:"#/properties/filters/properties/min_num_views/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs81 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_num_views !== undefined){let data31 = data3.max_num_views;const _errs84 = errors;if((!(((typeof data31 == "number") && (!(data31 % 1) && !isNaN(data31))) && (isFinite(data31)))) && (data31 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_num_views",schemaPath:"#/properties/filters/properties/max_num_views/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs84 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_num_impressions !== undefined){let data32 = data3.min_num_impressions;const _errs87 = errors;if((!(((typeof data32 == "number") && (!(data32 % 1) && !isNaN(data32))) && (isFinite(data32)))) && (data32 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_num_impressions",schemaPath:"#/properties/filters/properties/min_num_impressions/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs87 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_num_impressions !== undefined){let data33 = data3.max_num_impressions;const _errs90 = errors;if((!(((typeof data33 == "number") && (!(data33 % 1) && !isNaN(data33))) && (isFinite(data33)))) && (data33 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_num_impressions",schemaPath:"#/properties/filters/properties/max_num_impressions/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs90 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_num_referrals !== undefined){let data34 = data3.min_num_referrals;const _errs93 = errors;if((!(((typeof data34 == "number") && (!(data34 % 1) && !isNaN(data34))) && (isFinite(data34)))) && (data34 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_num_referrals",schemaPath:"#/properties/filters/properties/min_num_referrals/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs93 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_num_referrals !== undefined){let data35 = data3.max_num_referrals;const _errs96 = errors;if((!(((typeof data35 == "number") && (!(data35 % 1) && !isNaN(data35))) && (isFinite(data35)))) && (data35 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_num_referrals",schemaPath:"#/properties/filters/properties/max_num_referrals/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs96 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_published_on !== undefined){let data36 = data3.min_published_on;const _errs99 = errors;if((typeof data36 !== "string") && (data36 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_published_on",schemaPath:"#/properties/filters/properties/min_published_on/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs99 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_published_on !== undefined){let data37 = data3.max_published_on;const _errs102 = errors;if((typeof data37 !== "string") && (data37 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_published_on",schemaPath:"#/properties/filters/properties/max_published_on/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs102 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_created_at !== undefined){let data38 = data3.min_created_at;const _errs105 = errors;if((typeof data38 !== "string") && (data38 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_created_at",schemaPath:"#/properties/filters/properties/min_created_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs105 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_created_at !== undefined){let data39 = data3.max_created_at;const _errs108 = errors;if((typeof data39 !== "string") && (data39 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_created_at",schemaPath:"#/properties/filters/properties/max_created_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs108 === errors;}else {var valid1 = true;}if(valid1){if(data3.min_updated_at !== undefined){let data40 = data3.min_updated_at;const _errs111 = errors;if((typeof data40 !== "string") && (data40 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/min_updated_at",schemaPath:"#/properties/filters/properties/min_updated_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs111 === errors;}else {var valid1 = true;}if(valid1){if(data3.max_updated_at !== undefined){let data41 = data3.max_updated_at;const _errs114 = errors;if((typeof data41 !== "string") && (data41 !== null)){validate20.errors = [{instancePath:instancePath+"/filters/max_updated_at",schemaPath:"#/properties/filters/properties/max_updated_at/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs114 === errors;}else {var valid1 = true;}}}}}}}}}}}}}}}}}}}}}}}}}}}}}else {validate20.errors = [{instancePath:instancePath+"/filters",schemaPath:"#/properties/filters/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}}var valid0 = _errs11 === errors;}else {var valid0 = true;}}}}}}}else {validate20.errors = [{instancePath,schemaPath:"#/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}}validate20.errors = vErrors;return errors === 0;}