import {
    getDbTranslation,
    debug,
} from '../../../../../lib/helpers';
import TelegramBot from 'node-telegram-bot-api';
import {
    getIntegrationById,
    getNumPromptsByIntegrationConversationId,
    insertIntegrationMessage,
    getRecentIntegrationMessages
} from '../../../../../lib/db';
import { detectLanguage } from '../../../../../lib/language';
import { integrationShouldRespond } from '../../../../../lib/integration';
import { decryptBackend } from '../../../../../lib/crypt';
import { handleCompletion, createMessageObject } from '../../../../../lib/completion';
import {
    get200Response,
    get403Response,
    get500Response,
    get503Response,
} from '../../../../../lib/response';
import { getAgentByHost } from '../../../../../lib/agent';
import { transformCompletion } from '../../../../../lib/format';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const maxDuration = (parseInt(process.env.RESPONSE_TIMEOUT) / 1000);

// Handle the chat completion request
export async function POST(req, { params }) {

    const { id } = params;
    const integration = await getIntegrationById(id);
    if (!integration) {
        return get503Response();
    }

    const reqPayload= await req.json();

    const conversationId = reqPayload?.message?.chat?.id;
    const prompt = reqPayload?.message?.text ?? "";
    const userId = reqPayload?.message?.from?.id;

    // get agent
    const agent = await getAgentByHost(req.headers.get('host'));
    const lng = agent.supported_languages.includes(reqPayload.message.from.language_code) ? reqPayload.message.from.language_code : process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    //const lng = await detectLanguage(prompt);  //compare telegram languate to our codes, skip if needed
    const cuePhrase = getDbTranslation(integration.cue, lng);

    // Log the message
    const messageId = await insertIntegrationMessage({
        message: prompt,
        language: lng,
        integration_id: integration.id,
        conversation_id: conversationId,
        user_id: userId,
    });


    if (integrationShouldRespond(prompt, cuePhrase)) {

        const client = new TelegramBot(await decryptBackend(integration.secret));

        if (!client) {
            return get403Response('Incorrect Telegram Credentials');
        }

       // const agent = await getAgentByHost(req.headers.get('host'));
        const agent = await getAgentByHost(req.headers.get('host'));
        const numPreviousResponses = await getNumPromptsByIntegrationConversationId(agent.id, integration.id, conversationId);

        // If there is no record of previous responses in this conversation, send the welcome message if it exists
        const welcome = getDbTranslation(integration.welcome, lng);
        if ((numPreviousResponses === 0) && welcome) {
            if (numPreviousResponses === 0 && welcome) {
                await client.sendMessage(conversationId, welcome);
                }
        }

        const messageRows = await getRecentIntegrationMessages(integration.id, conversationId, parseInt(agent.max_memories) * 2);
        let messages = [];
        messageRows.reverse().forEach((message) => {
            messages.push(createMessageObject(message.user_id ? 'user' : 'assistant', message.message));
        });

        const payload = {
            messages: messages,
            stream: false,
            metadata: {
                session: String(conversationId), //chatid
                conversation: String(conversationId),
                language: lng,
            },
            user: String(conversationId), //userid
            response_format: { type: 'json' },
        };

        const res = await handleCompletion(
            payload,
            req,
            integration
        );

        if (res.status === 200) {

            const json = await res.json();
            let response = json.choices[0].message.content;
            const cta = json.cta;
            if (cta) {
                response += `\n\n${cta.content}`;
            }

            try {

               const reply = transformCompletion(response, false, true);

                await client.sendMessage(conversationId, reply);

                const messageId = await insertIntegrationMessage({
                    message: response,
                    language: lng,
                    integration_id: integration.id,
                    conversation_id: conversationId,
                    user_id: null,
                });

            } catch (error) {
                const errorMsg = `Error sending Twilio message: ${error.message}`;
                debug(true, 'ERROR', errorMsg);
                return get500Response(errorMsg);
            }

        } else {
            return res;
        }

    } else {
        // debug(`NO CUE PHRASE (${cuePhrase}): ${prompt}`);
    }

    return get200Response(null);

    // SmsMessageSid=SM85008720a29db38111de7cd7614a576f&NumMedia=0&ProfileName=Jake&MessageType=text&SmsSid=SM85008720a29db38111de7cd7614a576f&WaId=***********&SmsStatus=received&Body=Hello&To=whatsapp%3A%2B14155238886&NumSegments=1&ReferralNumMedia=0&MessageSid=SM85008720a29db38111de7cd7614a576f&AccountSid=AC61f47d03e4570334a766ce30a1ce2b3f&From=whatsapp%3A%2B***********&ApiVersion=2010-04-01

}
//{"update_id":*********,
//"message":{"message_id":4,"from":{"id":**********,"is_bot":false,"first_name":"Sara","last_name":"Carlile","language_code":"en"},"chat":{"id":**********,"first_name":"Sara","last_name":"Carlile","type":"private"},"date":**********,"text":"Hi Apologist AI!"}}
