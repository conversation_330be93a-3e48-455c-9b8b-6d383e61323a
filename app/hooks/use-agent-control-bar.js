import * as React from 'react';
import { Track } from 'livekit-client';
import {
  useLocalParticipant,
  usePersistentUserChoices,
  useRoomContext,
  useTrackToggle,
} from '@livekit/components-react';
import { usePublishPermissions } from './use-publish-permissions';

/**
 * @typedef {Object} ControlBarControls
 * @property {boolean} [microphone] - Whether microphone control is visible
 * @property {boolean} [screenShare] - Whether screen share control is visible
 * @property {boolean} [chat] - Whether chat control is visible
 * @property {boolean} [camera] - Whether camera control is visible
 * @property {boolean} [leave] - Whether leave control is visible
 */

/**
 * @typedef {Object} UseAgentControlBarProps
 * @property {ControlBarControls} [controls] - Control visibility settings
 * @property {boolean} [saveUserChoices] - Whether to save user choices
 * @property {(error: {source: import('livekit-client').Track.Source, error: Error}) => void} [onDeviceError] - Device error handler
 */

/**
 * @typedef {Object} UseAgentControlBarReturn
 * @property {import('@livekit/components-react').TrackReferenceOrPlaceholder} micTrackRef - Microphone track reference
 * @property {ControlBarControls} visibleControls - Visible controls
 * @property {ReturnType<typeof useTrackToggle>} microphoneToggle - Microphone toggle
 * @property {ReturnType<typeof useTrackToggle>} cameraToggle - Camera toggle
 * @property {ReturnType<typeof useTrackToggle>} screenShareToggle - Screen share toggle
 * @property {() => void} handleDisconnect - Disconnect handler
 * @property {(deviceId: string) => void} handleAudioDeviceChange - Audio device change handler
 * @property {(deviceId: string) => void} handleVideoDeviceChange - Video device change handler
 */

/**
 * Hook for managing agent control bar functionality
 * @param {UseAgentControlBarProps} [props={}] - Hook props
 * @returns {UseAgentControlBarReturn} Control bar functionality
 */
export function useAgentControlBar(props = {}) {
  const { controls, saveUserChoices = true } = props;

  // Check if localStorage is available
  const isLocalStorageAvailable = React.useMemo(() => {
    try {
      return typeof window !== 'undefined' && window.localStorage && localStorage.getItem;
    } catch (error) {
      console.warn('Local storage is not available:', error);
      return false;
    }
  }, []);

  const shouldSaveUserChoices = saveUserChoices && isLocalStorageAvailable;
  const visibleControls = {
    leave: true,
    ...controls,
  };
  const { microphoneTrack, localParticipant } = useLocalParticipant();
  const publishPermissions = usePublishPermissions();
  const room = useRoomContext();

  const microphoneToggle = useTrackToggle({
    source: Track.Source.Microphone,
    onDeviceError: (error) => props.onDeviceError?.({ source: Track.Source.Microphone, error }),
  });
  const cameraToggle = useTrackToggle({
    source: Track.Source.Camera,
    onDeviceError: (error) => props.onDeviceError?.({ source: Track.Source.Camera, error }),
  });
  const screenShareToggle = useTrackToggle({
    source: Track.Source.ScreenShare,
    onDeviceError: (error) => props.onDeviceError?.({ source: Track.Source.ScreenShare, error }),
  });

  const micTrackRef = React.useMemo(() => {
    return {
      participant: localParticipant,
      source: Track.Source.Microphone,
      publication: microphoneTrack,
    };
  }, [localParticipant, microphoneTrack]);

  visibleControls.microphone ??= publishPermissions.microphone;
  visibleControls.screenShare ??= publishPermissions.screenShare;
  visibleControls.camera ??= publishPermissions.camera;
  visibleControls.chat ??= publishPermissions.data;

  const {
    saveAudioInputEnabled,
    saveAudioInputDeviceId,
    saveVideoInputEnabled,
    saveVideoInputDeviceId,
  } = usePersistentUserChoices({
    preventSave: !shouldSaveUserChoices,
  });

  const handleDisconnect = React.useCallback(() => {
    if (room) {
      room.disconnect();
    }
  }, [room]);

  const handleAudioDeviceChange = React.useCallback(
    (deviceId) => {
      saveAudioInputDeviceId(deviceId ?? 'default');
    },
    [saveAudioInputDeviceId]
  );

  const handleVideoDeviceChange = React.useCallback(
    (deviceId) => {
      saveVideoInputDeviceId(deviceId ?? 'default');
    },
    [saveVideoInputDeviceId]
  );

  const handleToggleCamera = React.useCallback(
    async (enabled) => {
      if (screenShareToggle.enabled) {
        screenShareToggle.toggle(false);
      }
      await cameraToggle.toggle(enabled);
      // persist video input enabled preference
      saveVideoInputEnabled(!cameraToggle.enabled);
    },
    [cameraToggle.enabled, screenShareToggle.enabled]
  );

  const handleToggleMicrophone = React.useCallback(
    async (enabled) => {
      await microphoneToggle.toggle(enabled);
      // persist audio input enabled preference
      saveAudioInputEnabled(!microphoneToggle.enabled);
    },
    [microphoneToggle.enabled]
  );

  const handleToggleScreenShare = React.useCallback(
    async (enabled) => {
      if (cameraToggle.enabled) {
        cameraToggle.toggle(false);
      }
      await screenShareToggle.toggle(enabled);
    },
    [screenShareToggle.enabled, cameraToggle.enabled]
  );

  return {
    micTrackRef,
    visibleControls,
    cameraToggle: {
      ...cameraToggle,
      toggle: handleToggleCamera,
    },
    microphoneToggle: {
      ...microphoneToggle,
      toggle: handleToggleMicrophone,
    },
    screenShareToggle: {
      ...screenShareToggle,
      toggle: handleToggleScreenShare,
    },
    handleDisconnect,
    handleAudioDeviceChange,
    handleVideoDeviceChange,
  };
}
