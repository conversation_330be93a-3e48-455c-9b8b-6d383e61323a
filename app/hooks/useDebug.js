import * as React from 'react';
import { setLogLevel } from 'livekit-client';
import { useRoomContext } from '@livekit/components-react';

/**
 * Hook to enable debug mode for LiveKit
 * @param {{logLevel?: import('livekit-client').LogLevel}} [options={}] - Debug options
 */
export const useDebugMode = ({ logLevel } = {}) => {
  const room = useRoomContext();

  React.useEffect(() => {
    setLogLevel(logLevel ?? 'debug');

    // eslint-disable-next-line no-underscore-dangle
    window.__lk_room = room;

    return () => {
      // eslint-disable-next-line no-underscore-dangle
      window.__lk_room = undefined;
    };
  }, [room, logLevel]);
};
